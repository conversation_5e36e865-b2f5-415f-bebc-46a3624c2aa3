@echo off
setlocal enabledelayedexpansion

:: =============================================================================
:: Civitai Explorer Project Quick Start Script
:: =============================================================================

title Civitai Explorer - Project Launcher
color 0A

echo.
echo ===============================================================================
echo                          Civitai Explorer Launcher                          
echo                                                                              
echo                     Modern Image and Metadata Management App                               
echo ===============================================================================
echo.

:: Check current directory
if not exist "package.json" (
    echo [ERROR] package.json file not found
    echo Please run this script in the project root directory
    echo.
    pause
    exit /b 1
)

:: Check Node.js
echo [INFO] Checking Node.js environment...
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js not found
    echo Please install Node.js first: https://nodejs.org/
    echo.
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo [OK] Node.js version: !NODE_VERSION!
)

:: Check package manager
echo [INFO] Checking package manager...
pnpm --version >nul 2>&1
if errorlevel 1 (
    echo [WARN] pnpm not found, using npm
    set PACKAGE_MANAGER=npm
    set DEV_COMMAND=npm run dev
) else (
    for /f "tokens=*" %%i in ('pnpm --version') do set PNPM_VERSION=%%i
    echo [OK] pnpm version: !PNPM_VERSION!
    set PACKAGE_MANAGER=pnpm
    set DEV_COMMAND=pnpm dev
)

:: Main menu
:menu
echo.
echo ===============================================================================
echo                                Operation Menu                                   
echo ===============================================================================
echo   [1] Quick Start Dev Server (Recommended)                                             
echo   [2] Install/Update Dependencies                                                        
echo   [3] Build Production Version                                                         
echo   [4] Start Production Server                                                        
echo   [5] Run Code Check (ESLint)                                                 
echo   [6] Clean Cache and Reinstall                                                     
echo   [7] Show Project Info                                                          
echo   [8] Open App in Browser                                                     
echo   [0] Exit                                                                  
echo ===============================================================================
echo.

set /p choice="Please select operation (0-8): "

if "%choice%"=="1" goto start_dev
if "%choice%"=="2" goto install_deps
if "%choice%"=="3" goto build_prod
if "%choice%"=="4" goto start_prod
if "%choice%"=="5" goto run_lint
if "%choice%"=="6" goto clean_install
if "%choice%"=="7" goto show_info
if "%choice%"=="8" goto open_browser
if "%choice%"=="0" goto exit
echo [ERROR] Invalid choice, please try again
goto menu

:: Start development server
:start_dev
echo.
echo [INFO] Starting development server...
echo ===============================================================================
echo.

:: Check dependencies
if not exist "node_modules" (
    echo [INFO] First run, installing dependencies...
    %PACKAGE_MANAGER% install
    if errorlevel 1 (
        echo [ERROR] Dependencies installation failed
        pause
        goto menu
    )
    echo [OK] Dependencies installed successfully
    echo.
)

echo [INFO] Starting Civitai Explorer...
echo [INFO] Local URL: http://localhost:3000
echo [INFO] Network URL: http://0.0.0.0:3000
echo.
echo [TIP] Press Ctrl+C to stop the server
echo [TIP] Code changes will auto-reload
echo [TIP] First startup may take a few seconds
echo.

:: Open browser after delay
timeout /t 3 /nobreak >nul
start http://localhost:3000

%DEV_COMMAND%
goto menu

:: Install dependencies
:install_deps
echo.
echo [INFO] Installing/updating project dependencies...
echo ===============================================================================
%PACKAGE_MANAGER% install
if errorlevel 1 (
    echo [ERROR] Dependencies installation failed
) else (
    echo [OK] Dependencies installed successfully
)
echo.
pause
goto menu

:: Build production
:build_prod
echo.
echo [INFO] Building production version...
echo ===============================================================================
%PACKAGE_MANAGER% run build
if errorlevel 1 (
    echo [ERROR] Build failed
) else (
    echo [OK] Build successful
    echo [INFO] Build files are in .next directory
)
echo.
pause
goto menu

:: Start production server
:start_prod
echo.
echo [INFO] Starting production server...
echo ===============================================================================

:: Check if built
if not exist ".next" (
    echo [WARN] Build files not found, building...
    %PACKAGE_MANAGER% run build
    if errorlevel 1 (
        echo [ERROR] Build failed, cannot start production server
        pause
        goto menu
    )
)

echo [INFO] Production server starting...
echo [INFO] Access URL: http://localhost:3000
echo.
start http://localhost:3000
%PACKAGE_MANAGER% run start
goto menu

:: Run lint
:run_lint
echo.
echo [INFO] Running ESLint code check...
echo ===============================================================================
%PACKAGE_MANAGER% run lint
echo.
pause
goto menu

:: Clean install
:clean_install
echo.
echo [INFO] Cleaning cache and reinstalling...
echo ===============================================================================

echo [INFO] Removing node_modules...
if exist "node_modules" rmdir /s /q "node_modules"

echo [INFO] Removing package-lock.json...
if exist "package-lock.json" del "package-lock.json"

echo [INFO] Removing pnpm-lock.yaml...
if exist "pnpm-lock.yaml" del "pnpm-lock.yaml"

echo [INFO] Cleaning Next.js cache...
if exist ".next" rmdir /s /q ".next"

echo [INFO] Reinstalling dependencies...
%PACKAGE_MANAGER% install

if errorlevel 1 (
    echo [ERROR] Reinstallation failed
) else (
    echo [OK] Clean installation completed
)
echo.
pause
goto menu

:: Show project info
:show_info
echo.
echo [INFO] Project Information
echo ===============================================================================
echo Project Name: Civitai Explorer
echo Version: 0.1.0
echo Framework: Next.js 15.2.4 + React 19
echo Package Manager: !PACKAGE_MANAGER!
echo UI Library: Radix UI + Tailwind CSS
echo Dev Port: 3000
echo Project Path: %CD%
echo.
echo Available Scripts:
echo   dev    - Start development server
echo   build  - Build production version  
echo   start  - Start production server
echo   lint   - Run code check
echo.
echo Related Links:
echo   Next.js: https://nextjs.org/
echo   React: https://react.dev/
echo   Tailwind CSS: https://tailwindcss.com/
echo   Civitai API: https://github.com/civitai/civitai/wiki/REST-API-Reference
echo.
pause
goto menu

:: Open browser
:open_browser
echo.
echo [INFO] Opening app in browser...
start http://localhost:3000
echo [OK] Browser opened
echo [TIP] If app is not running, please start dev server first (option 1)
echo.
pause
goto menu

:: Exit
:exit
echo.
echo [INFO] Thank you for using Civitai Explorer Launcher!
echo [INFO] Project Path: %CD%
echo [INFO] For help, please check project documentation
echo.
echo Have a great day!
echo.
pause
exit /b 0
