C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:24914 Download the React DevTools for a better development experience: https://react.dev/link/react-devtools
contentSelector-csui.js:136 ctx sn
floatingSphere-csui.js:347 ctx Es
utils-csui.js:115 ctx Lt
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
Unchecked runtime.lastError: The message port closed before a response was received.
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.classicBlue in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.freshGreen in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.elegantPurple in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.warmRed in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.vibrantOrange in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.coolCyan in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.sweetPink in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.deepIndigo in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.professionalGray in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.minimalNeutral in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.classicBlue in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.freshGreen in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.elegantPurple in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.warmRed in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.vibrantOrange in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.coolCyan in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.sweetPink in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.deepIndigo in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.professionalGray in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.minimalNeutral in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.classicBlue in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.freshGreen in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.elegantPurple in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.warmRed in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.vibrantOrange in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.coolCyan in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.sweetPink in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.deepIndigo in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.professionalGray in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.minimalNeutral in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.classicBlue in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.freshGreen in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.elegantPurple in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.warmRed in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.vibrantOrange in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.coolCyan in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.sweetPink in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.deepIndigo in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.professionalGray in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.minimalNeutral in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:264 Applying theme: neutral
C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:316 Theme applied successfully: neutral
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.classicBlue in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.freshGreen in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.elegantPurple in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.warmRed in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.vibrantOrange in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.coolCyan in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.sweetPink in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.deepIndigo in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.professionalGray in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.minimalNeutral in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.classicBlue in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.freshGreen in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.elegantPurple in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.warmRed in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.vibrantOrange in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.coolCyan in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.sweetPink in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.deepIndigo in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.professionalGray in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.minimalNeutral in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
floatingSphere-csui.js:264 Calling function getSettings with arguments: []
floatingSphere-csui.js:132 sendToBackground response Object
floatingSphere-csui.js:347 loginStatus Object
C:\src\client\components\react-dev-overlay\app\hot-reloader-client.tsx:371 [Fast Refresh] rebuilding
C:\src\client\components\react-dev-overlay\app\hot-reloader-client.tsx:116 [Fast Refresh] done in 44ms
C:\src\client\components\react-dev-overlay\app\hot-reloader-client.tsx:116 [Fast Refresh] done in 55ms
