; NSIS installer script for Civitai Explorer

; Custom installer pages
!include "MUI2.nsh"

; Installer settings
!define MUI_ICON "public\icon.ico"
!define MUI_UNICON "public\icon.ico"
!define MUI_HEADERIMAGE
!define <PERSON>UI_HEADERIMAGE_BITMAP "public\header.bmp"
!define MUI_WELCOMEFINISHPAGE_BITMAP "public\welcome.bmp"

; Pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "LICENSE"
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

; Uninstaller pages
!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; Languages
!insertmacro MUI_LANGUAGE "English"
!insertmacro MUI_LANGUAGE "SimpChinese"

; Custom functions
Function .onInit
  ; Check if application is already running
  System::Call 'kernel32::CreateMutex(i 0, i 0, t "CivitaiExplorerMutex") i .r1 ?e'
  Pop $R0
  StrCmp $R0 0 +3
    MessageBox MB_OK|MB_ICONEXCLAMATION "Civitai Explorer is already running. Please close it first."
    Abort
FunctionEnd
