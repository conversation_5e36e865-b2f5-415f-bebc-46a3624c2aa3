"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect } from "react"
import type { CivitaiImage, CivitaiMetadata } from "@/types/civitai"
import * as CivitaiAPI from "@/lib/civitai-api"

interface CivitaiContextType {
  images: CivitaiImage[]
  metadata: Record<string, CivitaiMetadata>
  isLoading: boolean
  error: string | null
  apiKey: string | null
  downloadProgress: { current: number; total: number } | null
  setApiKey: (key: string | null) => void
  downloadImage: (params: DownloadParams) => Promise<void>
  deleteImage: (id: string) => void
  clearAllData: () => void
  getApiLimits: () => CivitaiAPI.ApiLimits
}

interface DownloadParams {
  username?: string
  modelId?: string
  modelVersionId?: string
  limit?: number
  onComplete?: (stats: { total: number; new: number; duplicates: number }) => void
}

const CivitaiContext = createContext<CivitaiContextType | undefined>(undefined)

export function useCivitai() {
  const context = useContext(CivitaiContext)
  if (context === undefined) {
    throw new Error("useCivitai must be used within a CivitaiProvider")
  }
  return context
}

export function CivitaiProvider({ children }: { children: React.ReactNode }) {
  const [images, setImages] = useState<CivitaiImage[]>([])
  const [metadata, setMetadata] = useState<Record<string, CivitaiMetadata>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [apiKey, setApiKey] = useState<string | null>(null)
  const [downloadProgress, setDownloadProgress] = useState<{ current: number; total: number } | null>(null)

  // 加载保存的数据和API密钥
  useEffect(() => {
    try {
      const savedImages = localStorage.getItem("civitai-images")
      const savedMetadata = localStorage.getItem("civitai-metadata")
      const savedApiKey = localStorage.getItem("civitai-api-key")

      if (savedImages) {
        setImages(JSON.parse(savedImages))
      }

      if (savedMetadata) {
        setMetadata(JSON.parse(savedMetadata))
      }

      if (savedApiKey) {
        setApiKey(savedApiKey)
      }
    } catch (err) {
      console.error("Error loading saved data:", err)
      setError("Failed to load saved data")
    }
  }, [])

  // 保存数据到localStorage
  useEffect(() => {
    if (images.length > 0) {
      localStorage.setItem("civitai-images", JSON.stringify(images))
    }

    if (Object.keys(metadata).length > 0) {
      localStorage.setItem("civitai-metadata", JSON.stringify(metadata))
    }
  }, [images, metadata])

  // 保存API密钥
  useEffect(() => {
    if (apiKey) {
      localStorage.setItem("civitai-api-key", apiKey)
    } else {
      localStorage.removeItem("civitai-api-key")
    }
  }, [apiKey])

  const downloadImage = async (params: DownloadParams) => {
    setIsLoading(true)
    setError(null)
    setDownloadProgress(null)

    try {
      const limit = params.limit || 20 // 默认下载20张图片
      let apiResponse: { items: any[]; totalFetched: number; totalAvailable: number }

      // 进度回调函数
      const onProgress = (current: number, total: number) => {
        setDownloadProgress({ current, total })
      }

      // 根据不同参数调用不同的API
      if (params.username) {
        console.log(`开始下载用户 ${params.username} 的图像，限制: ${limit}`)
        apiResponse = await CivitaiAPI.getImagesByUsername(params.username, limit, apiKey, onProgress)
      } else if (params.modelId) {
        console.log(`开始下载模型 ${params.modelId} 的图像，限制: ${limit}`)
        apiResponse = await CivitaiAPI.getImagesByModelId(params.modelId, limit, apiKey, onProgress)
      } else if (params.modelVersionId) {
        console.log(`开始下载模型版本 ${params.modelVersionId} 的图像，限制: ${limit}`)
        apiResponse = await CivitaiAPI.getImagesByModelVersionId(params.modelVersionId, limit, apiKey, onProgress)
      } else {
        throw new Error("必须提供username、modelId或modelVersionId中的一个")
      }

      // 处理API响应
      if (!apiResponse.items || apiResponse.items.length === 0) {
        throw new Error("未找到图像")
      }

      // 转换API响应为应用格式
      const newImages: CivitaiImage[] = []
      const newMetadata: Record<string, CivitaiMetadata> = {}
      let duplicateCount = 0

      for (const item of apiResponse.items) {
        const appImage = CivitaiAPI.convertApiImageToAppImage(item)
        const appMetadata = CivitaiAPI.convertApiImageToAppMetadata(item)

        // 检查是否已存在（避免重复）
        const existingImage = images.find((img) => img.id === appImage.id)
        if (!existingImage) {
          newImages.push(appImage)
          newMetadata[appImage.id] = appMetadata
        } else {
          duplicateCount++
        }
      }

      // 更新状态
      setImages((prev) => [...prev, ...newImages])
      setMetadata((prev) => ({ ...prev, ...newMetadata }))

      console.log(
        `成功下载了 ${apiResponse.totalFetched} 张图像（新增 ${newImages.length} 张，重复 ${duplicateCount} 张），总可用: ${apiResponse.totalAvailable}`,
      )

      // 调用完成回调
      if (params.onComplete) {
        params.onComplete({
          total: apiResponse.totalFetched,
          new: newImages.length,
          duplicates: duplicateCount,
        })
      }

      // 如果获取的图像少于可用的图像，显示提示
      if (apiResponse.totalFetched < apiResponse.totalAvailable) {
        const limits = CivitaiAPI.getApiLimits(apiKey)
        if (!apiKey) {
          setError(
            `已下载 ${apiResponse.totalFetched} 张图像，但还有 ${apiResponse.totalAvailable - apiResponse.totalFetched} 张可用。添加 API 密钥可以下载更多图像。`,
          )
        } else if (apiResponse.totalFetched >= limits.maxTotal) {
          setError(`已达到最大下载限制 (${limits.maxTotal} 张图像)`)
        }
      }
    } catch (err: any) {
      console.error("Error downloading from Civitai:", err)
      setError(err.message || "Failed to download from Civitai")

      // 调用完成回调，但传递错误状态
      if (params.onComplete) {
        params.onComplete({
          total: 0,
          new: 0,
          duplicates: 0,
        })
      }
    } finally {
      setIsLoading(false)
      setDownloadProgress(null)
    }
  }

  const deleteImage = (id: string) => {
    setImages((prev) => prev.filter((img) => img.id !== id))

    // 创建一个新的metadata对象，不包含被删除的图像
    const newMetadata = { ...metadata }
    delete newMetadata[id]
    setMetadata(newMetadata)
  }

  const clearAllData = () => {
    setImages([])
    setMetadata({})
    localStorage.removeItem("civitai-images")
    localStorage.removeItem("civitai-metadata")
  }

  const getApiLimits = () => {
    return CivitaiAPI.getApiLimits(apiKey)
  }

  const value = {
    images,
    metadata,
    isLoading,
    error,
    apiKey,
    downloadProgress,
    setApiKey,
    downloadImage,
    deleteImage,
    clearAllData,
    getApiLimits,
  }

  return <CivitaiContext.Provider value={value}>{children}</CivitaiContext.Provider>
}
