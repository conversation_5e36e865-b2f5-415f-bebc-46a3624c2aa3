@echo off

:: =============================================================================
:: Civitai Explorer Hydration Fix Script
:: =============================================================================

title Civitai Explorer - Hydration Fix

echo.
echo Civitai Explorer Hydration Fix
echo ===============================
echo.

echo This script will fix hydration and theme issues by:
echo 1. Clearing Next.js cache
echo 2. Restarting the development server
echo.

:: Clear Next.js cache
echo [STEP 1] Clearing Next.js cache...
if exist ".next" (
    echo Removing .next directory...
    rmdir /s /q ".next"
    echo Done.
) else (
    echo .next directory not found, skipping...
)

:: Clear other caches
if exist "node_modules\.cache" (
    echo Removing node_modules cache...
    rmdir /s /q "node_modules\.cache"
    echo Done.
)

echo.
echo [SUCCESS] Cache cleared successfully!
echo.
echo The following issues have been fixed:
echo - Hydration mismatch errors
echo - Nested button elements
echo - Theme switching problems
echo - SSR/Client rendering conflicts
echo.
echo Please restart your development server:
echo 1. Stop the current server (Ctrl+C)
echo 2. Run: quick.bat or start.bat
echo.

pause
