"use client"
import { AppSidebar } from "@/components/app-sidebar"
import { SidebarProvider } from "@/components/ui/sidebar"
import { ThemeProvider } from "@/components/theme-provider"
import { ThemeTransitionProvider } from "@/components/theme-transition-manager"
import { MainContent } from "@/components/main-content"
import { CivitaiProvider } from "@/components/civitai/civitai-provider"
import { NavigationProvider } from "@/components/navigation-provider"
import { LanguageProvider } from "@/contexts/language-context"

export default function Home() {
  return (
    <LanguageProvider>
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
        <ThemeTransitionProvider>
          <CivitaiProvider>
            <NavigationProvider>
              <SidebarProvider defaultOpen={true}>
                <AppSidebar />
                <MainContent />
              </SidebarProvider>
            </NavigationProvider>
          </CivitaiProvider>
        </ThemeTransitionProvider>
      </ThemeProvider>
    </LanguageProvider>
  )
}
