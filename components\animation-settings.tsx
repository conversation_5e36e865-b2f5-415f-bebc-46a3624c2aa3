"use client"

import * as React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>rkles, Zap, Waves, RotateCcw } from "lucide-react"
import { useLanguage } from "@/contexts/language-context"

export type AnimationType = "shimmer" | "ripple" | "fade" | "slide"

interface AnimationOption {
  id: AnimationType
  name: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  preview: string
}

interface AnimationSettingsProps {
  currentAnimation: AnimationType
  onAnimationChange: (animation: AnimationType) => void
}

export function AnimationSettings({ currentAnimation, onAnimationChange }: AnimationSettingsProps) {
  const [previewAnimation, setPreviewAnimation] = React.useState<AnimationType | null>(null)
  const { t } = useLanguage()

  const animationOptions: AnimationOption[] = [
    {
      id: "shimmer",
      name: t("animation.shimmer"),
      description: t("animation.shimmerDesc"),
      icon: Sparkles,
      preview: "✨ 闪光效果预览",
    },
    {
      id: "ripple",
      name: t("animation.ripple"),
      description: t("animation.rippleDesc"),
      icon: Waves,
      preview: "🌊 波纹效果预览",
    },
    {
      id: "fade",
      name: t("animation.fade"),
      description: t("animation.fadeDesc"),
      icon: Zap,
      preview: "💫 淡化效果预览",
    },
    {
      id: "slide",
      name: t("animation.slide"),
      description: t("animation.slideDesc"),
      icon: RotateCcw,
      preview: "📱 滑动效果预览",
    },
  ]

  const handlePreview = (animationType: AnimationType) => {
    if (previewAnimation) return

    console.log("Previewing animation:", animationType)
    setPreviewAnimation(animationType)

    const previewElement = document.createElement("div")
    previewElement.className = `animation-preview animation-preview-${animationType}`

    switch (animationType) {
      case "shimmer":
        previewElement.style.cssText = `
          position: fixed;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.4), transparent);
          z-index: 9999;
          pointer-events: none;
          animation: shimmerPreview 1s ease-in-out;
        `
        break
      case "ripple":
        previewElement.style.cssText = `
          position: fixed;
          top: 50%;
          left: 50%;
          width: 0;
          height: 0;
          background: radial-gradient(circle, rgba(59, 130, 246, 0.4) 0%, transparent 70%);
          border-radius: 50%;
          z-index: 9999;
          pointer-events: none;
          transform: translate(-50%, -50%);
          animation: ripplePreview 1s ease-out;
        `
        break
      case "fade":
        previewElement.style.cssText = `
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(59, 130, 246, 0.3);
          z-index: 9999;
          pointer-events: none;
          opacity: 0;
          animation: fadePreview 1s ease-in-out;
        `
        break
      case "slide":
        previewElement.style.cssText = `
          position: fixed;
          top: -100%;
          left: 0;
          right: 0;
          height: 100%;
          background: linear-gradient(180deg, rgba(59, 130, 246, 0.4), rgba(147, 51, 234, 0.4));
          z-index: 9999;
          pointer-events: none;
          animation: slidePreview 1s ease-in-out;
        `
        break
    }

    document.body.appendChild(previewElement)

    const glassElements = document.querySelectorAll(".frosted-card, .frosted-sidebar")
    glassElements.forEach((element) => {
      element.classList.remove(
        "theme-switching-shimmer",
        "theme-switching-ripple",
        "theme-switching-fade",
        "theme-switching-slide",
      )
      element.classList.add(`theme-switching-${animationType}`)
    })

    console.log(`Applied preview animation ${animationType} to ${glassElements.length} elements`)

    setTimeout(() => {
      if (document.body.contains(previewElement)) {
        document.body.removeChild(previewElement)
      }

      glassElements.forEach((element) => {
        element.classList.remove(
          "theme-switching-shimmer",
          "theme-switching-ripple",
          "theme-switching-fade",
          "theme-switching-slide",
        )
      })

      setPreviewAnimation(null)
      console.log("Preview animation completed")
    }, 1000)
  }

  const handleAnimationChange = (animation: AnimationType) => {
    console.log("Animation changed to:", animation)
    onAnimationChange(animation)
  }

  return (
    <Card className="frosted-card theme-border theme-shadow">
      <CardHeader>
        <CardTitle className="text-base flex items-center gap-2 theme-text">
          <div className="p-2 rounded-lg theme-gradient">
            <Sparkles className="h-4 w-4 text-white" />
          </div>
          {t("animation.title")}
        </CardTitle>
        <CardDescription>{t("animation.description")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <RadioGroup value={currentAnimation} onValueChange={handleAnimationChange}>
          {animationOptions.map((option) => (
            <div
              key={option.id}
              className="flex items-center justify-between p-3 rounded-lg theme-accent theme-border hover:theme-secondary transition-colors"
            >
              <div className="flex items-center space-x-3">
                <RadioGroupItem value={option.id} id={option.id} className="theme-ring" />
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg theme-gradient">
                    <option.icon className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <Label htmlFor={option.id} className="font-medium cursor-pointer theme-text">
                      {option.name}
                    </Label>
                    <p className="text-xs text-slate-600 dark:text-slate-400">{option.description}</p>
                  </div>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePreview(option.id)}
                disabled={previewAnimation === option.id}
                className="text-xs theme-border theme-ring text-slate-700 dark:text-slate-200 hover:text-slate-900 dark:hover:text-slate-100"
              >
                {previewAnimation === option.id ? t("button.previewing") : t("button.preview")}
              </Button>
            </div>
          ))}
        </RadioGroup>

        <div className="mt-4 p-3 rounded-lg theme-accent theme-border">
          <p className="text-xs theme-text">{t("animation.tip")}</p>
        </div>
      </CardContent>
    </Card>
  )
}
