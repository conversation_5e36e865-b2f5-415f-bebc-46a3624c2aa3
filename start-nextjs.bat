@echo off

:: =============================================================================
:: Start Next.js Development Server Only
:: =============================================================================

title Next.js Development Server

echo.
echo Starting Next.js Development Server
echo ===================================
echo.

echo This will start only the Next.js development server.
echo After this starts successfully, you can run start-electron.bat
echo.

:: Check if package.json exists
if not exist "package.json" (
    echo [ERROR] package.json not found
    echo Please run this script in the project root directory
    pause
    exit /b 1
)

:: Install dependencies if needed
if not exist "node_modules" (
    echo [INFO] Installing dependencies...
    npm install
    if errorlevel 1 (
        echo [ERROR] Failed to install dependencies
        pause
        exit /b 1
    )
)

echo [INFO] Starting Next.js development server...
echo [INFO] Server will be available at: http://localhost:3000
echo [INFO] Press Ctrl+C to stop the server
echo.

npm run dev

echo.
echo Next.js development server stopped
pause
