'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

// 简化的主题定义
const simpleThemes = {
  default: {
    primary: 'rgb(59, 130, 246)', // blue-500
    primaryForeground: 'rgb(255, 255, 255)',
    secondary: 'rgb(239, 246, 255)', // blue-50
  },
  emerald: {
    primary: 'rgb(16, 185, 129)', // emerald-500
    primaryForeground: 'rgb(255, 255, 255)',
    secondary: 'rgb(236, 253, 245)', // emerald-50
  },
  violet: {
    primary: 'rgb(139, 92, 246)', // violet-500
    primaryForeground: 'rgb(255, 255, 255)',
    secondary: 'rgb(245, 243, 255)', // violet-50
  },
  rose: {
    primary: 'rgb(244, 63, 94)', // rose-500
    primaryForeground: 'rgb(255, 255, 255)',
    secondary: 'rgb(255, 241, 242)', // rose-50
  }
}

export function SimpleThemeTest() {
  const [currentTheme, setCurrentTheme] = useState<keyof typeof simpleThemes>('default')
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return <div>Loading...</div>
  }

  const theme = simpleThemes[currentTheme]

  return (
    <div className="p-6 space-y-6">
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">简化主题测试</h2>
        <p className="text-muted-foreground">使用直接 RGB 值测试主题切换</p>
        
        {/* 主题选择器 */}
        <div className="flex gap-2">
          {Object.keys(simpleThemes).map((themeName) => (
            <button
              key={themeName}
              onClick={() => setCurrentTheme(themeName as keyof typeof simpleThemes)}
              className="px-3 py-1 rounded text-sm border"
              style={{
                backgroundColor: currentTheme === themeName ? simpleThemes[themeName as keyof typeof simpleThemes].primary : 'transparent',
                color: currentTheme === themeName ? simpleThemes[themeName as keyof typeof simpleThemes].primaryForeground : 'inherit',
                borderColor: simpleThemes[themeName as keyof typeof simpleThemes].primary
              }}
            >
              {themeName}
            </button>
          ))}
        </div>
      </div>

      {/* 测试按钮 */}
      <Card>
        <CardHeader>
          <CardTitle>直接样式测试</CardTitle>
          <CardDescription>使用内联样式直接应用主题颜色</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-3">
            <button
              className="px-4 py-2 rounded-md transition-all hover:opacity-90"
              style={{
                backgroundColor: theme.primary,
                color: theme.primaryForeground,
                border: `1px solid ${theme.primary}`
              }}
            >
              主题按钮
            </button>
            
            <button
              className="px-4 py-2 rounded-md transition-all hover:opacity-90"
              style={{
                backgroundColor: 'transparent',
                color: theme.primary,
                border: `2px solid ${theme.primary}`
              }}
            >
              轮廓按钮
            </button>
            
            <div
              className="px-3 py-1 rounded-full text-sm"
              style={{
                backgroundColor: theme.secondary,
                color: theme.primary,
                border: `1px solid ${theme.primary}`
              }}
            >
              主题徽章
            </div>
            
            <div
              className="px-3 py-1 rounded-full text-sm"
              style={{
                backgroundColor: theme.primary,
                color: theme.primaryForeground
              }}
            >
              实心徽章
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 测试卡片 */}
      <Card
        style={{
          backgroundColor: theme.secondary,
          borderColor: theme.primary,
          borderWidth: '1px'
        }}
      >
        <CardHeader>
          <CardTitle style={{ color: theme.primary }}>主题卡片</CardTitle>
          <CardDescription>这个卡片使用了主题颜色</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm">
            卡片背景使用主题的次要颜色，边框和标题使用主题的主要颜色。
          </p>
          <div className="mt-4">
            <a 
              href="#" 
              style={{ color: theme.primary }}
              className="text-sm hover:underline"
            >
              这是一个主题链接
            </a>
          </div>
        </CardContent>
      </Card>

      {/* 当前主题信息 */}
      <Card>
        <CardHeader>
          <CardTitle>当前主题信息</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <strong>主色:</strong>
              <div className="flex items-center gap-2 mt-1">
                <div 
                  className="w-4 h-4 rounded border"
                  style={{ backgroundColor: theme.primary }}
                />
                <code>{theme.primary}</code>
              </div>
            </div>
            <div>
              <strong>主色前景:</strong>
              <div className="flex items-center gap-2 mt-1">
                <div 
                  className="w-4 h-4 rounded border"
                  style={{ backgroundColor: theme.primaryForeground }}
                />
                <code>{theme.primaryForeground}</code>
              </div>
            </div>
            <div>
              <strong>次要色:</strong>
              <div className="flex items-center gap-2 mt-1">
                <div 
                  className="w-4 h-4 rounded border"
                  style={{ backgroundColor: theme.secondary }}
                />
                <code>{theme.secondary}</code>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
