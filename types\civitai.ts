export interface CivitaiImage {
  id: string
  url: string
  name: string
  createdAt: string
  modelId: string
  modelVersionId: string
  username: string
}

export interface CivitaiMetadata {
  id: string
  modelInfo: {
    name: string
    description: string
    type: string
    nsfw: boolean
    tags: string[]
  }
  imageInfo: {
    width: number
    height: number
    hash: string
    size: number
  }
  generationParams: {
    prompt: string
    negativePrompt: string
    steps: number
    sampler: string
    cfgScale: string
    seed: number
  }
  user: {
    username: string
    profilePicture: string
  }
}
