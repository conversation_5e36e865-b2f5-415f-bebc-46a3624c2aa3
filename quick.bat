@echo off

:: =============================================================================
:: Civitai Explorer Quick Start (Simple Version)
:: =============================================================================

title Civitai Explorer - Quick Start

echo.
echo Civitai Explorer Quick Start
echo =============================
echo.

:: Check project files
if not exist "package.json" (
    echo ERROR: Please run this script in the project root directory
    pause
    exit /b 1
)

:: Check and install dependencies
if not exist "node_modules" (
    echo Installing dependencies for first run...

    :: Try pnpm first, fallback to npm
    pnpm --version >nul 2>&1
    if errorlevel 1 (
        echo Using npm to install dependencies...
        npm install --legacy-peer-deps
        if errorlevel 1 (
            echo Trying with --force flag...
            npm install --force
        )
    ) else (
        echo Using pnpm to install dependencies...
        pnpm install --legacy-peer-deps
        if errorlevel 1 (
            echo Trying with --force flag...
            pnpm install --force
        )
    )

    if errorlevel 1 (
        echo ERROR: Dependencies installation failed
        echo Try running start.bat and select option 7 to fix dependencies
        pause
        exit /b 1
    )
    echo Dependencies installed successfully
)

:: Start development server
echo.
echo Starting development server...
echo Local URL: http://localhost:3000
echo Press Ctrl+C to stop the server
echo.

:: Open browser after 2 seconds
timeout /t 2 /nobreak >nul
start http://localhost:3000

:: Check package manager and start
pnpm --version >nul 2>&1
if errorlevel 1 (
    npm run dev
) else (
    pnpm dev
)

echo.
echo Server stopped
pause
