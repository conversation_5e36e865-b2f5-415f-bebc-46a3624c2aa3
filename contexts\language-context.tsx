"use client"

import * as React from "react"

export type Language = "zh" | "en"

interface LanguageContextType {
  language: Language
  setLanguage: (language: Language) => void
  t: (key: string) => string
}

const LanguageContext = React.createContext<LanguageContextType | undefined>(undefined)

export function useLanguage() {
  const context = React.useContext(LanguageContext)
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider")
  }
  return context
}

// 语言配置
const translations = {
  zh: {
    // 应用标题
    "app.title": "Civitai Explorer",
    "app.subtitle": "图像与元数据管理器",
    "app.version": "v1.0.0",
    "app.description": "增强毛玻璃界面",

    // 导航
    "nav.dashboard": "仪表板",
    "nav.gallery": "图像画廊",
    "nav.downloader": "下载管理器",
    "nav.metadata": "元数据浏览器",
    "nav.settings": "设置",
    "nav.appearance": "外观",
    "nav.navigation": "导航",

    // 主题
    "theme.lightMode": "浅色模式",
    "theme.darkMode": "深色模式",
    "theme.colorTheme": "主题色彩",
    "theme.settings": "主题设置",
    "theme.preview": "主题预览",
    "theme.description": "查看当前主题的效果",

    // 颜色主题
    "color.default": "默认蓝色",
    "color.emerald": "翡翠绿",
    "color.violet": "紫罗兰",
    "color.rose": "玫瑰红",
    "color.amber": "琥珀橙",
    "color.cyan": "青色",
    "color.pink": "粉色",
    "color.indigo": "靛蓝",
    "color.slate": "石板灰",
    "color.neutral": "中性灰",

    // 按钮
    "button.primary": "主要按钮",
    "button.secondary": "次要按钮",
    "button.text": "文本按钮",
    "button.preview": "预览",
    "button.previewing": "预览中...",
    "button.download": "下载",
    "button.delete": "删除",
    "button.clear": "清除",
    "button.export": "导出",
    "button.submit": "提交",
    "button.submitting": "提交中...",

    // 仪表板
    "dashboard.welcome": "欢迎使用 Civitai Explorer",
    "dashboard.description": "从 Civitai 下载和管理图像及元数据",
    "dashboard.images": "图像",
    "dashboard.metadata": "元数据",
    "dashboard.status": "状态",
    "dashboard.ready": "就绪",
    "dashboard.operational": "系统运行正常",
    "dashboard.downloadedImages": "已下载图像",
    "dashboard.metadataRecords": "元数据记录",
    "dashboard.features": "功能特性",
    "dashboard.quickStart": "快速开始",
    "dashboard.quickStartDesc": "使用下载管理器开始从 Civitai 下载图像。通过侧边栏导航访问不同功能并管理您下载的内容。",

    // 功能标签
    "feature.frostedGlass": "毛玻璃界面",
    "feature.civitaiApi": "Civitai API",
    "feature.metadataManagement": "元数据管理",
    "feature.themeSystem": "主题系统",

    // 下载器
    "downloader.title": "Civitai 下载器",
    "downloader.byUsername": "按用户名",
    "downloader.byModelId": "按模型ID",
    "downloader.byVersionId": "按版本ID",
    "downloader.username": "Civitai 用户名",
    "downloader.modelId": "模型ID",
    "downloader.versionId": "模型版本ID",
    "downloader.enterUsername": "输入用户名",
    "downloader.enterModelId": "输入模型ID",
    "downloader.enterVersionId": "输入模型版本ID",
    "downloader.downloading": "下载中...",
    "downloader.downloaded": "已下载",
    "downloader.downloadByUsername": "按用户名下载图像",
    "downloader.downloadByModelId": "按模型ID下载图像",
    "downloader.downloadByVersionId": "按版本ID下载图像",
    "downloader.howToUse": "使用方法",
    "downloader.instructions": [
      "输入 Civitai 用户名以下载该用户的图像",
      "输入模型ID以下载与该模型相关的图像",
      "输入模型版本ID以下载特定版本的图像",
      "下载的图像和元数据将保存在本地",
      "在图像画廊标签页中查看您下载的图像",
    ],

    // 画廊
    "gallery.title": "图像画廊",
    "gallery.search": "搜索图像...",
    "gallery.clearAll": "清除全部",
    "gallery.noImages": "未找到图像。使用图像下载器从 Civitai 下载图像。",
    "gallery.noMatches": "没有图像匹配您的搜索。请尝试不同的搜索词。",
    "gallery.viewMetadata": "查看元数据",
    "gallery.downloadImage": "下载图像",
    "gallery.confirmDelete": "确定要删除所有图像和元数据吗？",

    // 元数据
    "metadata.title": "元数据浏览器",
    "metadata.overview": "元数据概览",
    "metadata.summary": "已下载元数据摘要",
    "metadata.totalRecords": "总记录数",
    "metadata.models": "模型",
    "metadata.users": "用户",
    "metadata.noMetadata": "没有可用的元数据。下载一些图像以在此处查看元数据。",
    "metadata.moreRecords": "还有 {count} 条记录...",
    "metadata.imageMetadata": "图像元数据",
    "metadata.detailedInfo": "关于此图像的详细信息",

    // 元数据标签页
    "metadata.tabs.model": "模型信息",
    "metadata.tabs.image": "图像信息",
    "metadata.tabs.generation": "生成参数",
    "metadata.tabs.user": "用户信息",
    "metadata.tabs.raw": "原始JSON",

    // 元数据字段
    "metadata.type": "类型",
    "metadata.nsfw": "NSFW",
    "metadata.tags": "标签",
    "metadata.dimensions": "尺寸",
    "metadata.fileSize": "文件大小",
    "metadata.hash": "哈希值",
    "metadata.prompt": "提示词",
    "metadata.negativePrompt": "负面提示词",
    "metadata.steps": "步数",
    "metadata.sampler": "采样器",
    "metadata.cfgScale": "CFG比例",
    "metadata.seed": "种子",
    "metadata.creator": "Civitai 创作者",
    "metadata.yes": "是",
    "metadata.no": "否",

    // 设置
    "settings.title": "设置",
    "settings.themeSettings": "主题设置",
    "settings.themeDescription": "自定义应用程序的外观和感觉",
    "settings.dataManagement": "数据管理",
    "settings.dataDescription": "管理您下载的图像和元数据",
    "settings.exportData": "导出数据",
    "settings.exportDescription": "将所有图像和元数据下载为JSON文件",
    "settings.clearData": "清除所有数据",
    "settings.clearDescription": "删除所有下载的图像和元数据",
    "settings.confirmClear": "确定要删除所有图像和元数据吗？此操作无法撤销。",
    "settings.appInfo": "应用程序信息",
    "settings.appInfoDescription": "关于此应用程序的信息",
    "settings.version": "版本",
    "settings.framework": "UI框架",
    "settings.designSystem": "设计系统",
    "settings.resolution": "分辨率",

    // 语言设置
    "settings.language": "语言设置",
    "settings.languageDescription": "选择应用程序的显示语言",
    "settings.selectLanguage": "选择语言",
    "language.chinese": "中文",
    "language.english": "English",

    // 主题设置面板
    "themePanel.title": "主题设置",
    "themePanel.description": "自定义应用程序的外观和感觉",
    "themePanel.lightDark": "明暗模式",
    "themePanel.lightDarkDesc": "选择浅色或深色主题",
    "themePanel.light": "浅色",
    "themePanel.dark": "深色",
    "themePanel.system": "系统",
    "themePanel.colorTheme": "主题色彩",
    "themePanel.colorThemeDesc": "选择你喜欢的主题颜色",
    "themePanel.visualEffects": "视觉效果",
    "themePanel.visualEffectsDesc": "自定义界面的视觉效果",
    "themePanel.glassEffect": "毛玻璃效果",
    "themePanel.glassEffectDesc": "启用背景模糊和半透明效果",
    "themePanel.animations": "动画效果",
    "themePanel.animationsDesc": "启用过渡动画和悬停效果",
    "themePanel.animationSpeed": "动画速度",
    "themePanel.slow": "慢速",
    "themePanel.standard": "标准",
    "themePanel.fast": "快速",

    // 动画设置
    "animation.title": "主题切换动画",
    "animation.description": "选择主题切换时的动画效果",
    "animation.shimmer": "闪光扫过",
    "animation.shimmerDesc": "经典的闪光从左到右扫过效果",
    "animation.ripple": "波纹扩散",
    "animation.rippleDesc": "从中心向外扩散的波纹效果",
    "animation.fade": "淡入淡出",
    "animation.fadeDesc": "平滑的透明度渐变效果",
    "animation.slide": "滑动切换",
    "animation.slideDesc": "从上到下的滑动遮罩效果",
    "animation.tip": "💡 提示：选择动画效果后，切换主题色彩或明暗模式时会看到对应的动画效果",

    // 通用
    "common.loading": "加载中...",
    "common.error": "错误",
    "common.success": "成功",
    "common.cancel": "取消",
    "common.confirm": "确认",
    "common.save": "保存",
    "common.close": "关闭",
    "common.back": "返回",
    "common.next": "下一步",
    "common.previous": "上一步",
  },
  en: {
    // App title
    "app.title": "Civitai Explorer",
    "app.subtitle": "Image & Metadata Manager",
    "app.version": "v1.0.0",
    "app.description": "Enhanced Frosted Glass UI",

    // Navigation
    "nav.dashboard": "Dashboard",
    "nav.gallery": "Image Gallery",
    "nav.downloader": "Download Manager",
    "nav.metadata": "Metadata Explorer",
    "nav.settings": "Settings",
    "nav.appearance": "Appearance",
    "nav.navigation": "Navigation",

    // Theme
    "theme.lightMode": "Light Mode",
    "theme.darkMode": "Dark Mode",
    "theme.colorTheme": "Color Theme",
    "theme.settings": "Theme Settings",
    "theme.preview": "Theme Preview",
    "theme.description": "View the current theme effects",

    // Color themes
    "color.default": "Default Blue",
    "color.emerald": "Emerald Green",
    "color.violet": "Violet Purple",
    "color.rose": "Rose Red",
    "color.amber": "Amber Orange",
    "color.cyan": "Cyan Blue",
    "color.pink": "Pink",
    "color.indigo": "Indigo Blue",
    "color.slate": "Slate Gray",
    "color.neutral": "Neutral Gray",

    // Buttons
    "button.primary": "Primary Button",
    "button.secondary": "Secondary Button",
    "button.text": "Text Button",
    "button.preview": "Preview",
    "button.previewing": "Previewing...",
    "button.download": "Download",
    "button.delete": "Delete",
    "button.clear": "Clear",
    "button.export": "Export",
    "button.submit": "Submit",
    "button.submitting": "Submitting...",

    // Dashboard
    "dashboard.welcome": "Welcome to Civitai Explorer",
    "dashboard.description": "Download and manage images and metadata from Civitai",
    "dashboard.images": "Images",
    "dashboard.metadata": "Metadata",
    "dashboard.status": "Status",
    "dashboard.ready": "Ready",
    "dashboard.operational": "System operational",
    "dashboard.downloadedImages": "Downloaded images",
    "dashboard.metadataRecords": "Metadata records",
    "dashboard.features": "Features",
    "dashboard.quickStart": "Quick Start",
    "dashboard.quickStartDesc":
      "Use the Download Manager to start downloading images from Civitai. Navigate through the sidebar to access different features and manage your downloaded content.",

    // Feature tags
    "feature.frostedGlass": "Frosted Glass UI",
    "feature.civitaiApi": "Civitai API",
    "feature.metadataManagement": "Metadata Management",
    "feature.themeSystem": "Theme System",

    // Downloader
    "downloader.title": "Civitai Downloader",
    "downloader.byUsername": "By Username",
    "downloader.byModelId": "By Model ID",
    "downloader.byVersionId": "By Version ID",
    "downloader.username": "Civitai Username",
    "downloader.modelId": "Model ID",
    "downloader.versionId": "Model Version ID",
    "downloader.enterUsername": "Enter username",
    "downloader.enterModelId": "Enter model ID",
    "downloader.enterVersionId": "Enter model version ID",
    "downloader.downloading": "Downloading...",
    "downloader.downloaded": "Downloaded",
    "downloader.downloadByUsername": "Download Images by Username",
    "downloader.downloadByModelId": "Download Images by Model ID",
    "downloader.downloadByVersionId": "Download Images by Version ID",
    "downloader.howToUse": "How to use",
    "downloader.instructions": [
      "Enter a Civitai username to download images from that user",
      "Enter a model ID to download images associated with that model",
      "Enter a model version ID to download images for a specific version",
      "Downloaded images and metadata will be saved locally",
      "View your downloaded images in the Image Gallery tab",
    ],

    // Gallery
    "gallery.title": "Image Gallery",
    "gallery.search": "Search images...",
    "gallery.clearAll": "Clear All",
    "gallery.noImages": "No images found. Use the Image Downloader to download images from Civitai.",
    "gallery.noMatches": "No images match your search. Try a different search term.",
    "gallery.viewMetadata": "View Metadata",
    "gallery.downloadImage": "Download Image",
    "gallery.confirmDelete": "Are you sure you want to delete all images and metadata?",

    // Metadata
    "metadata.title": "Metadata Explorer",
    "metadata.overview": "Metadata Overview",
    "metadata.summary": "Summary of downloaded metadata",
    "metadata.totalRecords": "Total Records",
    "metadata.models": "Models",
    "metadata.users": "Users",
    "metadata.noMetadata": "No metadata available. Download some images to see metadata here.",
    "metadata.moreRecords": "And {count} more records...",
    "metadata.imageMetadata": "Image Metadata",
    "metadata.detailedInfo": "Detailed information about this image",

    // Metadata tabs
    "metadata.tabs.model": "Model Info",
    "metadata.tabs.image": "Image Info",
    "metadata.tabs.generation": "Generation Params",
    "metadata.tabs.user": "User Info",
    "metadata.tabs.raw": "Raw JSON",

    // Metadata fields
    "metadata.type": "Type",
    "metadata.nsfw": "NSFW",
    "metadata.tags": "Tags",
    "metadata.dimensions": "Dimensions",
    "metadata.fileSize": "File Size",
    "metadata.hash": "Hash",
    "metadata.prompt": "Prompt",
    "metadata.negativePrompt": "Negative Prompt",
    "metadata.steps": "Steps",
    "metadata.sampler": "Sampler",
    "metadata.cfgScale": "CFG Scale",
    "metadata.seed": "Seed",
    "metadata.creator": "Civitai Creator",
    "metadata.yes": "Yes",
    "metadata.no": "No",

    // Settings
    "settings.title": "Settings",
    "settings.themeSettings": "Theme Settings",
    "settings.themeDescription": "Customize the appearance of the application",
    "settings.dataManagement": "Data Management",
    "settings.dataDescription": "Manage your downloaded images and metadata",
    "settings.exportData": "Export Data",
    "settings.exportDescription": "Download all your images and metadata as a JSON file",
    "settings.clearData": "Clear All Data",
    "settings.clearDescription": "Remove all downloaded images and metadata",
    "settings.confirmClear": "Are you sure you want to delete all images and metadata? This action cannot be undone.",
    "settings.appInfo": "Application Info",
    "settings.appInfoDescription": "Information about this application",
    "settings.version": "Version",
    "settings.framework": "UI Framework",
    "settings.designSystem": "Design System",
    "settings.resolution": "Resolution",

    // Language settings
    "settings.language": "Language Settings",
    "settings.languageDescription": "Select the display language for the application",
    "settings.selectLanguage": "Select Language",
    "language.chinese": "中文",
    "language.english": "English",

    // Theme settings panel
    "themePanel.title": "Theme Settings",
    "themePanel.description": "Customize the appearance and feel of the application",
    "themePanel.lightDark": "Light/Dark Mode",
    "themePanel.lightDarkDesc": "Choose light or dark theme",
    "themePanel.light": "Light",
    "themePanel.dark": "Dark",
    "themePanel.system": "System",
    "themePanel.colorTheme": "Color Theme",
    "themePanel.colorThemeDesc": "Choose your preferred theme color",
    "themePanel.visualEffects": "Visual Effects",
    "themePanel.visualEffectsDesc": "Customize interface visual effects",
    "themePanel.glassEffect": "Glass Effect",
    "themePanel.glassEffectDesc": "Enable background blur and translucent effects",
    "themePanel.animations": "Animations",
    "themePanel.animationsDesc": "Enable transition animations and hover effects",
    "themePanel.animationSpeed": "Animation Speed",
    "themePanel.slow": "Slow",
    "themePanel.standard": "Standard",
    "themePanel.fast": "Fast",

    // Animation settings
    "animation.title": "Theme Transition Animation",
    "animation.description": "Choose animation effects for theme transitions",
    "animation.shimmer": "Shimmer Sweep",
    "animation.shimmerDesc": "Classic shimmer effect sweeping from left to right",
    "animation.ripple": "Ripple Spread",
    "animation.rippleDesc": "Ripple effect spreading from center outward",
    "animation.fade": "Fade In/Out",
    "animation.fadeDesc": "Smooth opacity transition effect",
    "animation.slide": "Slide Transition",
    "animation.slideDesc": "Sliding mask effect from top to bottom",
    "animation.tip":
      "💡 Tip: After selecting an animation effect, you'll see the corresponding animation when switching theme colors or light/dark mode",

    // Common
    "common.loading": "Loading...",
    "common.error": "Error",
    "common.success": "Success",
    "common.cancel": "Cancel",
    "common.confirm": "Confirm",
    "common.save": "Save",
    "common.close": "Close",
    "common.back": "Back",
    "common.next": "Next",
    "common.previous": "Previous",
  },
}

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguage] = React.useState<Language>("zh")

  // 加载保存的语言设置
  React.useEffect(() => {
    const savedLanguage = localStorage.getItem("app-language") as Language
    if (savedLanguage && ["zh", "en"].includes(savedLanguage)) {
      setLanguage(savedLanguage)
    }
  }, [])

  // 保存语言设置
  const handleSetLanguage = React.useCallback((newLanguage: Language) => {
    setLanguage(newLanguage)
    localStorage.setItem("app-language", newLanguage)
    console.log("Language changed to:", newLanguage)
  }, [])

  // 翻译函数
  const t = React.useCallback(
    (key: string) => {
      const translation = translations[language][key as keyof (typeof translations)[typeof language]]
      if (!translation) {
        console.warn(`Translation missing for key: ${key} in language: ${language}`)
        return key
      }
      return translation
    },
    [language],
  )

  const value = React.useMemo(
    () => ({
      language,
      setLanguage: handleSetLanguage,
      t,
    }),
    [language, handleSetLanguage, t],
  )

  return <LanguageContext.Provider value={value}>{children}</LanguageContext.Provider>
}
