Theme transition completed
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.classicBlue in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:141
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.freshGreen in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:152
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.elegantPurple in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:163
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.warmRed in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:174
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.vibrantOrange in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:185
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.coolCyan in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:196
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.sweetPink in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:207
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.deepIndigo in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:218
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.professionalGray in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:229
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.minimalNeutral in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:240
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.classicBlue in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:141
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.freshGreen in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:152
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.elegantPurple in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:163
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.warmRed in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:174
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.vibrantOrange in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:185
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.coolCyan in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:196
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.sweetPink in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:207
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.deepIndigo in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:218
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.professionalGray in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:229
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.minimalNeutral in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:240
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:258 Applying theme: slate
C:\Users\<USER>\Desktop\sidebar-app\components\theme-transition-manager.tsx:117 Starting theme transition with animation: shimmer
C:\Users\<USER>\Desktop\sidebar-app\components\theme-transition-manager.tsx:138 Creating shimmer effect
C:\Users\<USER>\Desktop\sidebar-app\components\theme-transition-manager.tsx:159 Applied shimmer animation to 33 elements
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.classicBlue in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:141
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performSyncWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16289
flushSyncWorkAcrossRoots_impl @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16137
flushSyncWork$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14712
f @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:24439
exports.flushSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js:140
dispatchDiscreteCustomEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\react-primitive\dist\index.mjs:37
handleSelect @ C:\Users\<USER>\Desktop\src\Menu.tsx:626
handleEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\primitive\dist\index.mjs:6
executeDispatch @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16426
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
processDispatchQueue @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16476
eval @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:17074
batchedUpdates$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:3253
dispatchEventForPluginEventSystem @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16630
dispatchEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20716
dispatchDiscreteEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20684
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.freshGreen in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:152
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performSyncWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16289
flushSyncWorkAcrossRoots_impl @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16137
flushSyncWork$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14712
f @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:24439
exports.flushSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js:140
dispatchDiscreteCustomEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\react-primitive\dist\index.mjs:37
handleSelect @ C:\Users\<USER>\Desktop\src\Menu.tsx:626
handleEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\primitive\dist\index.mjs:6
executeDispatch @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16426
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
processDispatchQueue @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16476
eval @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:17074
batchedUpdates$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:3253
dispatchEventForPluginEventSystem @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16630
dispatchEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20716
dispatchDiscreteEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20684
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.elegantPurple in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:163
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performSyncWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16289
flushSyncWorkAcrossRoots_impl @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16137
flushSyncWork$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14712
f @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:24439
exports.flushSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js:140
dispatchDiscreteCustomEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\react-primitive\dist\index.mjs:37
handleSelect @ C:\Users\<USER>\Desktop\src\Menu.tsx:626
handleEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\primitive\dist\index.mjs:6
executeDispatch @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16426
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
processDispatchQueue @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16476
eval @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:17074
batchedUpdates$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:3253
dispatchEventForPluginEventSystem @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16630
dispatchEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20716
dispatchDiscreteEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20684
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.warmRed in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:174
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performSyncWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16289
flushSyncWorkAcrossRoots_impl @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16137
flushSyncWork$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14712
f @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:24439
exports.flushSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js:140
dispatchDiscreteCustomEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\react-primitive\dist\index.mjs:37
handleSelect @ C:\Users\<USER>\Desktop\src\Menu.tsx:626
handleEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\primitive\dist\index.mjs:6
executeDispatch @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16426
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
processDispatchQueue @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16476
eval @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:17074
batchedUpdates$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:3253
dispatchEventForPluginEventSystem @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16630
dispatchEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20716
dispatchDiscreteEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20684
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.vibrantOrange in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:185
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performSyncWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16289
flushSyncWorkAcrossRoots_impl @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16137
flushSyncWork$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14712
f @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:24439
exports.flushSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js:140
dispatchDiscreteCustomEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\react-primitive\dist\index.mjs:37
handleSelect @ C:\Users\<USER>\Desktop\src\Menu.tsx:626
handleEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\primitive\dist\index.mjs:6
executeDispatch @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16426
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
processDispatchQueue @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16476
eval @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:17074
batchedUpdates$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:3253
dispatchEventForPluginEventSystem @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16630
dispatchEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20716
dispatchDiscreteEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20684
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.coolCyan in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:196
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performSyncWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16289
flushSyncWorkAcrossRoots_impl @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16137
flushSyncWork$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14712
f @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:24439
exports.flushSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js:140
dispatchDiscreteCustomEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\react-primitive\dist\index.mjs:37
handleSelect @ C:\Users\<USER>\Desktop\src\Menu.tsx:626
handleEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\primitive\dist\index.mjs:6
executeDispatch @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16426
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
processDispatchQueue @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16476
eval @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:17074
batchedUpdates$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:3253
dispatchEventForPluginEventSystem @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16630
dispatchEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20716
dispatchDiscreteEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20684
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.sweetPink in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:207
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performSyncWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16289
flushSyncWorkAcrossRoots_impl @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16137
flushSyncWork$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14712
f @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:24439
exports.flushSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js:140
dispatchDiscreteCustomEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\react-primitive\dist\index.mjs:37
handleSelect @ C:\Users\<USER>\Desktop\src\Menu.tsx:626
handleEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\primitive\dist\index.mjs:6
executeDispatch @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16426
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
processDispatchQueue @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16476
eval @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:17074
batchedUpdates$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:3253
dispatchEventForPluginEventSystem @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16630
dispatchEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20716
dispatchDiscreteEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20684
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.deepIndigo in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:218
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performSyncWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16289
flushSyncWorkAcrossRoots_impl @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16137
flushSyncWork$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14712
f @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:24439
exports.flushSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js:140
dispatchDiscreteCustomEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\react-primitive\dist\index.mjs:37
handleSelect @ C:\Users\<USER>\Desktop\src\Menu.tsx:626
handleEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\primitive\dist\index.mjs:6
executeDispatch @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16426
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
processDispatchQueue @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16476
eval @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:17074
batchedUpdates$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:3253
dispatchEventForPluginEventSystem @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16630
dispatchEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20716
dispatchDiscreteEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20684
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.professionalGray in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:229
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performSyncWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16289
flushSyncWorkAcrossRoots_impl @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16137
flushSyncWork$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14712
f @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:24439
exports.flushSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js:140
dispatchDiscreteCustomEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\react-primitive\dist\index.mjs:37
handleSelect @ C:\Users\<USER>\Desktop\src\Menu.tsx:626
handleEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\primitive\dist\index.mjs:6
executeDispatch @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16426
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
processDispatchQueue @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16476
eval @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:17074
batchedUpdates$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:3253
dispatchEventForPluginEventSystem @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16630
dispatchEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20716
dispatchDiscreteEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20684
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.minimalNeutral in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:240
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performSyncWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16289
flushSyncWorkAcrossRoots_impl @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16137
flushSyncWork$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14712
f @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:24439
exports.flushSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js:140
dispatchDiscreteCustomEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\react-primitive\dist\index.mjs:37
handleSelect @ C:\Users\<USER>\Desktop\src\Menu.tsx:626
handleEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\primitive\dist\index.mjs:6
executeDispatch @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16426
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
processDispatchQueue @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16476
eval @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:17074
batchedUpdates$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:3253
dispatchEventForPluginEventSystem @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16630
dispatchEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20716
dispatchDiscreteEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20684
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.classicBlue in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:141
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performSyncWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16289
flushSyncWorkAcrossRoots_impl @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16137
flushSyncWork$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14712
f @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:24439
exports.flushSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js:140
dispatchDiscreteCustomEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\react-primitive\dist\index.mjs:37
handleSelect @ C:\Users\<USER>\Desktop\src\Menu.tsx:626
handleEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\primitive\dist\index.mjs:6
executeDispatch @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16426
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
processDispatchQueue @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16476
eval @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:17074
batchedUpdates$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:3253
dispatchEventForPluginEventSystem @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16630
dispatchEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20716
dispatchDiscreteEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20684
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.freshGreen in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:152
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performSyncWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16289
flushSyncWorkAcrossRoots_impl @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16137
flushSyncWork$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14712
f @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:24439
exports.flushSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js:140
dispatchDiscreteCustomEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\react-primitive\dist\index.mjs:37
handleSelect @ C:\Users\<USER>\Desktop\src\Menu.tsx:626
handleEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\primitive\dist\index.mjs:6
executeDispatch @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16426
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
processDispatchQueue @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16476
eval @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:17074
batchedUpdates$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:3253
dispatchEventForPluginEventSystem @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16630
dispatchEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20716
dispatchDiscreteEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20684
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.elegantPurple in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:163
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performSyncWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16289
flushSyncWorkAcrossRoots_impl @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16137
flushSyncWork$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14712
f @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:24439
exports.flushSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js:140
dispatchDiscreteCustomEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\react-primitive\dist\index.mjs:37
handleSelect @ C:\Users\<USER>\Desktop\src\Menu.tsx:626
handleEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\primitive\dist\index.mjs:6
executeDispatch @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16426
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
processDispatchQueue @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16476
eval @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:17074
batchedUpdates$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:3253
dispatchEventForPluginEventSystem @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16630
dispatchEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20716
dispatchDiscreteEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20684
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.warmRed in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:174
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performSyncWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16289
flushSyncWorkAcrossRoots_impl @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16137
flushSyncWork$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14712
f @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:24439
exports.flushSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js:140
dispatchDiscreteCustomEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\react-primitive\dist\index.mjs:37
handleSelect @ C:\Users\<USER>\Desktop\src\Menu.tsx:626
handleEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\primitive\dist\index.mjs:6
executeDispatch @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16426
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
processDispatchQueue @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16476
eval @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:17074
batchedUpdates$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:3253
dispatchEventForPluginEventSystem @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16630
dispatchEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20716
dispatchDiscreteEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20684
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.vibrantOrange in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:185
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performSyncWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16289
flushSyncWorkAcrossRoots_impl @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16137
flushSyncWork$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14712
f @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:24439
exports.flushSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js:140
dispatchDiscreteCustomEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\react-primitive\dist\index.mjs:37
handleSelect @ C:\Users\<USER>\Desktop\src\Menu.tsx:626
handleEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\primitive\dist\index.mjs:6
executeDispatch @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16426
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
processDispatchQueue @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16476
eval @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:17074
batchedUpdates$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:3253
dispatchEventForPluginEventSystem @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16630
dispatchEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20716
dispatchDiscreteEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20684
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.coolCyan in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:196
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performSyncWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16289
flushSyncWorkAcrossRoots_impl @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16137
flushSyncWork$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14712
f @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:24439
exports.flushSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js:140
dispatchDiscreteCustomEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\react-primitive\dist\index.mjs:37
handleSelect @ C:\Users\<USER>\Desktop\src\Menu.tsx:626
handleEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\primitive\dist\index.mjs:6
executeDispatch @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16426
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
processDispatchQueue @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16476
eval @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:17074
batchedUpdates$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:3253
dispatchEventForPluginEventSystem @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16630
dispatchEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20716
dispatchDiscreteEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20684
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.sweetPink in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:207
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performSyncWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16289
flushSyncWorkAcrossRoots_impl @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16137
flushSyncWork$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14712
f @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:24439
exports.flushSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js:140
dispatchDiscreteCustomEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\react-primitive\dist\index.mjs:37
handleSelect @ C:\Users\<USER>\Desktop\src\Menu.tsx:626
handleEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\primitive\dist\index.mjs:6
executeDispatch @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16426
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
processDispatchQueue @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16476
eval @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:17074
batchedUpdates$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:3253
dispatchEventForPluginEventSystem @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16630
dispatchEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20716
dispatchDiscreteEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20684
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.deepIndigo in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:218
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performSyncWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16289
flushSyncWorkAcrossRoots_impl @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16137
flushSyncWork$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14712
f @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:24439
exports.flushSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js:140
dispatchDiscreteCustomEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\react-primitive\dist\index.mjs:37
handleSelect @ C:\Users\<USER>\Desktop\src\Menu.tsx:626
handleEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\primitive\dist\index.mjs:6
executeDispatch @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16426
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
processDispatchQueue @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16476
eval @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:17074
batchedUpdates$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:3253
dispatchEventForPluginEventSystem @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16630
dispatchEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20716
dispatchDiscreteEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20684
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.professionalGray in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:229
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performSyncWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16289
flushSyncWorkAcrossRoots_impl @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16137
flushSyncWork$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14712
f @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:24439
exports.flushSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js:140
dispatchDiscreteCustomEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\react-primitive\dist\index.mjs:37
handleSelect @ C:\Users\<USER>\Desktop\src\Menu.tsx:626
handleEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\primitive\dist\index.mjs:6
executeDispatch @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16426
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
processDispatchQueue @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16476
eval @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:17074
batchedUpdates$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:3253
dispatchEventForPluginEventSystem @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16630
dispatchEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20716
dispatchDiscreteEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20684
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.minimalNeutral in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:240
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performSyncWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16289
flushSyncWorkAcrossRoots_impl @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16137
flushSyncWork$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14712
f @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:24439
exports.flushSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom.development.js:140
dispatchDiscreteCustomEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\react-primitive\dist\index.mjs:37
handleSelect @ C:\Users\<USER>\Desktop\src\Menu.tsx:626
handleEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\@radix-ui\primitive\dist\index.mjs:6
executeDispatch @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16426
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
processDispatchQueue @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16476
eval @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:17074
batchedUpdates$1 @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:3253
dispatchEventForPluginEventSystem @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16630
dispatchEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20716
dispatchDiscreteEvent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:20684
2content_script.js:5837  Uncaught (in promise) fetchError: Failed to fetch
    at Zx (content_script.js:5837:26922)
    at u1.sendMessage (content_script.js:5837:26351)
    at async ot (content_script.js:6988:7519)
    at async content_script.js:6988:14024
Zx @ content_script.js:5837
sendMessage @ content_script.js:5837
C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:310 Theme applied successfully: slate
C:\Users\<USER>\Desktop\sidebar-app\components\theme-transition-manager.tsx:179 Theme transition completed
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.classicBlue in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:141
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.freshGreen in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:152
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.elegantPurple in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:163
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.warmRed in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:174
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.vibrantOrange in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:185
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.coolCyan in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:196
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.sweetPink in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:207
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.deepIndigo in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:218
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.professionalGray in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:229
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.minimalNeutral in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:240
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5078
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.classicBlue in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:141
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.freshGreen in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:152
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.elegantPurple in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:163
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.warmRed in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:174
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.vibrantOrange in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:185
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.coolCyan in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:196
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.sweetPink in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:207
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.deepIndigo in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:218
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.professionalGray in language: zh
LanguageProvider.useCallback[t] @ C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474
ThemeSelector @ C:\Users\<USER>\Desktop\sidebar-app\components\theme-selector.tsx:229
react-stack-bottom-frame @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:23949
renderWithHooksAgain @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5178
renderWithHooks @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:5090
updateFunctionComponent @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:8327
beginWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:9944
runWithFiberInDEV @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:1510
performUnitOfWork @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:15119
workLoopSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14943
renderRootSync @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14923
performWorkOnRoot @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:14410
performWorkOnRootViaSchedulerTask @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\react-dom\cjs\react-dom-client.development.js:16274
performWorkUntilDeadline @ C:\Users\<USER>\Desktop\sidebar-app\node_modules\next\dist\compiled\scheduler\cjs\scheduler.development.js:45
C:\Users\<USER>\Desktop\sidebar-app\contexts\language-context.tsx:474  Translation missing for key: theme.minimalNeutral in language: zh