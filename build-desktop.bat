@echo off

:: =============================================================================
:: Civitai Explorer Desktop App Build Script
:: =============================================================================

title Civitai Explorer - Desktop Build

echo.
echo Civitai Explorer Desktop App Builder
echo ====================================
echo.

echo This script will build the desktop application using Electron.
echo.
echo REQUIREMENTS:
echo - Node.js 18+ installed
echo - All dependencies installed (npm install)
echo - Project builds successfully (npm run build)
echo.

set /p confirm="Do you want to continue? (y/n): "
if /i not "%confirm%"=="y" (
    echo Build cancelled
    pause
    exit /b 0
)

echo.
echo [STEP 1] Installing Electron dependencies...
echo ============================================

:: Install Electron dependencies
npm install electron electron-builder electron-updater concurrently wait-on --save-dev

if errorlevel 1 (
    echo [ERROR] Failed to install Electron dependencies
    pause
    exit /b 1
)

echo [OK] Electron dependencies installed

echo.
echo [STEP 2] Building Next.js application...
echo ========================================

:: Build the Next.js app for static export
npm run build

if errorlevel 1 (
    echo [ERROR] Failed to build Next.js application
    pause
    exit /b 1
)

echo [OK] Next.js application built

echo.
echo [STEP 3] Exporting static files...
echo ==================================

:: Export static files
npm run export

if errorlevel 1 (
    echo [ERROR] Failed to export static files
    pause
    exit /b 1
)

echo [OK] Static files exported to 'out' directory

echo.
echo [STEP 4] Building desktop application...
echo ========================================

:: Build the desktop app
npm run electron-build-win

if errorlevel 1 (
    echo [ERROR] Failed to build desktop application
    pause
    exit /b 1
)

echo.
echo [SUCCESS] Desktop application built successfully!
echo ================================================
echo.
echo OUTPUT FILES:
echo - Installer: dist/Civitai Explorer Setup.exe
echo - Portable: dist/Civitai Explorer Portable.exe
echo.
echo NEXT STEPS:
echo 1. Test the installer by running it
echo 2. Test the portable version
echo 3. Distribute the files to users
echo.

pause

echo.
echo Would you like to open the dist folder?
set /p open="Open dist folder? (y/n): "
if /i "%open%"=="y" (
    explorer dist
)

echo.
echo Desktop build process completed!
echo.

pause
