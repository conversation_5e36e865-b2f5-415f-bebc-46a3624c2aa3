"use client"

import type { CivitaiMetadata } from "@/types/civitai"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { formatFileSize } from "@/lib/utils"
import { useLanguage } from "@/contexts/language-context"

interface MetadataViewerProps {
  metadata: CivitaiMetadata
}

export function MetadataViewer({ metadata }: MetadataViewerProps) {
  const { t } = useLanguage()

  return (
    <Tabs defaultValue="model" className="w-full">
      <TabsList className="frosted-glass">
        <TabsTrigger value="model">{t("metadata.tabs.model")}</TabsTrigger>
        <TabsTrigger value="image">{t("metadata.tabs.image")}</TabsTrigger>
        <TabsTrigger value="generation">{t("metadata.tabs.generation")}</TabsTrigger>
        <TabsTrigger value="user">{t("metadata.tabs.user")}</TabsTrigger>
        <TabsTrigger value="raw">{t("metadata.tabs.raw")}</TabsTrigger>
      </TabsList>

      <TabsContent value="model" className="mt-4 space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="text-lg font-medium text-slate-900 dark:text-slate-100">{metadata.modelInfo.name}</h3>
            <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">{metadata.modelInfo.description}</p>
          </div>
          <div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">{t("metadata.type")}</span>
              <span className="text-sm">{metadata.modelInfo.type}</span>
            </div>
            <Separator className="my-2" />
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">{t("metadata.nsfw")}</span>
              <span className="text-sm">{metadata.modelInfo.nsfw ? t("metadata.yes") : t("metadata.no")}</span>
            </div>
            <Separator className="my-2" />
            <div className="flex flex-col gap-2">
              <span className="text-sm font-medium">{t("metadata.tags")}</span>
              <div className="flex flex-wrap gap-2">
                {metadata.modelInfo.tags.map((tag) => (
                  <Badge key={tag} variant="outline" className="frosted-glass-hover">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </div>
      </TabsContent>

      <TabsContent value="image" className="mt-4 space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="aspect-video rounded-lg overflow-hidden">
            <img
              src={`https://picsum.photos/seed/${metadata.id}/800/600`}
              alt="Preview"
              className="w-full h-full object-cover"
            />
          </div>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">{t("metadata.dimensions")}</span>
              <span className="text-sm">
                {metadata.imageInfo.width} × {metadata.imageInfo.height}
              </span>
            </div>
            <Separator className="my-2" />
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">{t("metadata.fileSize")}</span>
              <span className="text-sm">{formatFileSize(metadata.imageInfo.size)}</span>
            </div>
            <Separator className="my-2" />
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">{t("metadata.hash")}</span>
              <span className="text-sm font-mono text-xs">{metadata.imageInfo.hash}</span>
            </div>
          </div>
        </div>
      </TabsContent>

      <TabsContent value="generation" className="mt-4 space-y-4">
        <div className="space-y-4">
          <div>
            <h3 className="text-sm font-medium mb-1">{t("metadata.prompt")}</h3>
            <div className="p-3 rounded-md bg-white/10 dark:bg-black/20 text-sm">
              {metadata.generationParams.prompt}
            </div>
          </div>

          <div>
            <h3 className="text-sm font-medium mb-1">{t("metadata.negativePrompt")}</h3>
            <div className="p-3 rounded-md bg-white/10 dark:bg-black/20 text-sm">
              {metadata.generationParams.negativePrompt}
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <h3 className="text-sm font-medium">{t("metadata.steps")}</h3>
              <p className="text-sm">{metadata.generationParams.steps}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium">{t("metadata.sampler")}</h3>
              <p className="text-sm">{metadata.generationParams.sampler}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium">{t("metadata.cfgScale")}</h3>
              <p className="text-sm">{metadata.generationParams.cfgScale}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium">{t("metadata.seed")}</h3>
              <p className="text-sm font-mono">{metadata.generationParams.seed}</p>
            </div>
          </div>
        </div>
      </TabsContent>

      <TabsContent value="user" className="mt-4 space-y-4">
        <div className="flex items-center gap-4">
          <div className="h-16 w-16 rounded-full overflow-hidden">
            <img
              src={metadata.user.profilePicture || "/placeholder.svg"}
              alt={metadata.user.username}
              className="h-full w-full object-cover"
            />
          </div>
          <div>
            <h3 className="text-lg font-medium">{metadata.user.username}</h3>
            <p className="text-sm text-slate-600 dark:text-slate-400">{t("metadata.creator")}</p>
          </div>
        </div>
      </TabsContent>

      <TabsContent value="raw" className="mt-4">
        <pre className="p-4 rounded-md bg-white/10 dark:bg-black/20 overflow-auto text-xs">
          {JSON.stringify(metadata, null, 2)}
        </pre>
      </TabsContent>
    </Tabs>
  )
}
