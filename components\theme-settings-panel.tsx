"use client"

import * as React from "react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Zap } from "lucide-react"
import { useTheme } from "next-themes"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { useLanguage } from "@/contexts/language-context"

export function ThemeSettingsPanel() {
  const { theme, setTheme } = useTheme()
  const [currentColorTheme, setCurrentColorTheme] = React.useState("default")
  const [glassEffect, setGlassEffect] = React.useState(true)
  const [animations, setAnimations] = React.useState(true)
  const [animationSpeed, setAnimationSpeed] = React.useState([1])
  const [isTransitioning, setIsTransitioning] = React.useState(false)
  const { t } = useLanguage()

  const colorThemes = [
    { name: t("color.default"), value: "default", color: "bg-blue-500" },
    { name: t("color.emerald"), value: "emerald", color: "bg-emerald-500" },
    { name: t("color.violet"), value: "violet", color: "bg-violet-500" },
    { name: t("color.rose"), value: "rose", color: "bg-rose-500" },
    { name: t("color.amber"), value: "amber", color: "bg-amber-500" },
    { name: t("color.cyan"), value: "cyan", color: "bg-cyan-500" },
    { name: t("color.pink"), value: "pink", color: "bg-pink-500" },
    { name: t("color.indigo"), value: "indigo", color: "bg-indigo-500" },
    { name: t("color.slate"), value: "slate", color: "bg-slate-500" },
    { name: t("color.neutral"), value: "neutral", color: "bg-neutral-500" },
  ]

  React.useEffect(() => {
    const savedColorTheme = localStorage.getItem("app-theme") || "default"
    const savedGlassEffect = localStorage.getItem("glass-effect") !== "false"
    const savedAnimations = localStorage.getItem("animations") !== "false"
    const savedAnimationSpeed = Number.parseFloat(localStorage.getItem("animation-speed") || "1")

    setCurrentColorTheme(savedColorTheme)
    setGlassEffect(savedGlassEffect)
    setAnimations(savedAnimations)
    setAnimationSpeed([savedAnimationSpeed])
  }, [])

  const applyColorTheme = async (themeValue: string) => {
    if (isTransitioning) return

    setIsTransitioning(true)

    // Add visual feedback
    const button = document.querySelector(`[data-theme-value="${themeValue}"]`)
    if (button) {
      button.classList.add("theme-switching")
    }

    // Apply theme with smooth transition
    await new Promise((resolve) => setTimeout(resolve, 150))

    document.documentElement.classList.remove(...colorThemes.map((t) => `theme-${t.value}`))
    document.documentElement.classList.add(`theme-${themeValue}`)
    localStorage.setItem("app-theme", themeValue)
    setCurrentColorTheme(themeValue)

    setTimeout(() => {
      if (button) {
        button.classList.remove("theme-switching")
      }
      setIsTransitioning(false)
    }, 400)
  }

  const toggleGlassEffect = (enabled: boolean) => {
    setGlassEffect(enabled)
    localStorage.setItem("glass-effect", enabled.toString())
    document.documentElement.classList.toggle("no-glass", !enabled)
  }

  const toggleAnimations = (enabled: boolean) => {
    setAnimations(enabled)
    localStorage.setItem("animations", enabled.toString())
    document.documentElement.classList.toggle("no-animations", !enabled)
  }

  const updateAnimationSpeed = (speed: number[]) => {
    setAnimationSpeed(speed)
    localStorage.setItem("animation-speed", speed[0].toString())

    // Update CSS custom properties for animation speed
    const speedMultiplier = speed[0]
    document.documentElement.style.setProperty("--theme-transition-duration", `${0.4 / speedMultiplier}s`)
    document.documentElement.style.setProperty("--color-transition-duration", `${0.3 / speedMultiplier}s`)
    document.documentElement.style.setProperty("--hover-transition-duration", `${0.2 / speedMultiplier}s`)
  }

  const handleThemeChange = async (newTheme: string) => {
    // Add transition effect to the entire page
    document.body.classList.add("theme-switching")

    await new Promise((resolve) => setTimeout(resolve, 100))
    setTheme(newTheme)

    setTimeout(() => {
      document.body.classList.remove("theme-switching")
    }, 400)
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="theme-transition-hover bg-white/20 dark:bg-white/10 border-white/30 dark:border-white/20 text-slate-700 dark:text-slate-300 hover:bg-white/30 dark:hover:bg-white/20"
        >
          <Settings className="h-4 w-4 mr-2" />
          {t("themePanel.title")}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl backdrop-blur-xl bg-white/95 dark:bg-slate-900/95 border-white/20 dark:border-white/10 backdrop-transition">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-slate-900 dark:text-slate-100">
            <Palette className="h-5 w-5" />
            {t("themePanel.title")}
          </DialogTitle>
          <DialogDescription className="text-slate-600 dark:text-slate-400">
            {t("themePanel.description")}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* 明暗模式 */}
          <Card className="theme-card backdrop-blur-xl bg-white/20 dark:bg-black/20 border-white/20 dark:border-white/10">
            <CardHeader>
              <CardTitle className="text-lg text-slate-900 dark:text-slate-100">{t("themePanel.lightDark")}</CardTitle>
              <CardDescription className="text-slate-600 dark:text-slate-400">
                {t("themePanel.lightDarkDesc")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RadioGroup value={theme} onValueChange={handleThemeChange} className="grid grid-cols-3 gap-4">
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="light" id="light" />
                  <Label htmlFor="light" className="flex items-center gap-2 cursor-pointer theme-transition-hover">
                    <Sun className="h-4 w-4" />
                    {t("themePanel.light")}
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="dark" id="dark" />
                  <Label htmlFor="dark" className="flex items-center gap-2 cursor-pointer theme-transition-hover">
                    <Moon className="h-4 w-4" />
                    {t("themePanel.dark")}
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="system" id="system" />
                  <Label htmlFor="system" className="flex items-center gap-2 cursor-pointer theme-transition-hover">
                    <Monitor className="h-4 w-4" />
                    {t("themePanel.system")}
                  </Label>
                </div>
              </RadioGroup>
            </CardContent>
          </Card>

          {/* 主题色彩 */}
          <Card className="theme-card backdrop-blur-xl bg-white/20 dark:bg-black/20 border-white/20 dark:border-white/10">
            <CardHeader>
              <CardTitle className="text-lg text-slate-900 dark:text-slate-100">{t("themePanel.colorTheme")}</CardTitle>
              <CardDescription className="text-slate-600 dark:text-slate-400">
                {t("themePanel.colorThemeDesc")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="theme-color-grid grid-cols-5 gap-3">
                {colorThemes.map((colorTheme, index) => (
                  <button
                    key={colorTheme.value}
                    data-theme-value={colorTheme.value}
                    onClick={() => applyColorTheme(colorTheme.value)}
                    disabled={isTransitioning}
                    className={`theme-color-item relative p-3 rounded-lg border-2 transition-all disabled:opacity-50 disabled:cursor-not-allowed ${
                      currentColorTheme === colorTheme.value
                        ? "border-slate-900 dark:border-slate-100"
                        : "border-transparent hover:border-slate-300 dark:hover:border-slate-600"
                    }`}
                  >
                    <div
                      className={`w-8 h-8 rounded-full ${colorTheme.color} mx-auto transition-transform duration-200 ${
                        currentColorTheme === colorTheme.value ? "scale-110" : "hover:scale-105"
                      }`}
                    />
                    <div className="text-xs mt-1 text-slate-700 dark:text-slate-300 text-center">{colorTheme.name}</div>
                    {currentColorTheme === colorTheme.value && (
                      <div className="absolute -top-1 -right-1 w-4 h-4 bg-slate-900 dark:bg-slate-100 rounded-full flex items-center justify-center theme-active-indicator">
                        <div className="w-2 h-2 bg-white dark:bg-slate-900 rounded-full" />
                      </div>
                    )}
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 视觉效果 */}
          <Card className="theme-card backdrop-blur-xl bg-white/20 dark:bg-black/20 border-white/20 dark:border-white/10">
            <CardHeader>
              <CardTitle className="text-lg text-slate-900 dark:text-slate-100">
                {t("themePanel.visualEffects")}
              </CardTitle>
              <CardDescription className="text-slate-600 dark:text-slate-400">
                {t("themePanel.visualEffectsDesc")}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-slate-900 dark:text-slate-100">{t("themePanel.glassEffect")}</Label>
                  <p className="text-sm text-slate-600 dark:text-slate-400">{t("themePanel.glassEffectDesc")}</p>
                </div>
                <Switch checked={glassEffect} onCheckedChange={toggleGlassEffect} />
              </div>

              <Separator className="bg-white/20 dark:bg-white/10" />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-slate-900 dark:text-slate-100 flex items-center gap-2">
                    <Sparkles className="h-4 w-4" />
                    {t("themePanel.animations")}
                  </Label>
                  <p className="text-sm text-slate-600 dark:text-slate-400">{t("themePanel.animationsDesc")}</p>
                </div>
                <Switch checked={animations} onCheckedChange={toggleAnimations} />
              </div>

              {animations && (
                <>
                  <Separator className="bg-white/20 dark:bg-white/10" />

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label className="text-slate-900 dark:text-slate-100 flex items-center gap-2">
                        <Zap className="h-4 w-4" />
                        {t("themePanel.animationSpeed")}
                      </Label>
                      <span className="text-sm text-slate-600 dark:text-slate-400">{animationSpeed[0]}x</span>
                    </div>
                    <Slider
                      value={animationSpeed}
                      onValueChange={updateAnimationSpeed}
                      max={3}
                      min={0.5}
                      step={0.1}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-slate-500 dark:text-slate-400">
                      <span>{t("themePanel.slow")}</span>
                      <span>{t("themePanel.standard")}</span>
                      <span>{t("themePanel.fast")}</span>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  )
}
