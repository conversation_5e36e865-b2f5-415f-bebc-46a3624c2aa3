"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useCivitai } from "@/components/civitai/civitai-provider"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Database, FileText, User, ImageIcon, TrendingUp, BarChart3 } from "lucide-react"
import { useLanguage } from "@/contexts/language-context"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Download, Trash2 } from "lucide-react"
import { useState } from "react"

export function MetadataPage() {
  const { metadata, images, clearAllData } = useCivitai()
  const { t } = useLanguage()

  const [showDeleteDialog, setShowDeleteDialog] = useState(false)

  const handleExportData = () => {
    const data = {
      images,
      metadata,
      exportDate: new Date().toISOString(),
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: "application/json" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `civitai-export-${new Date().toISOString().split("T")[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleClearData = () => {
    clearAllData()
    setShowDeleteDialog(false)
  }

  const metadataEntries = Object.entries(metadata)
  const uniqueModels = new Set(metadataEntries.map(([, meta]) => meta.modelInfo.name)).size
  const uniqueUsers = new Set(metadataEntries.map(([, meta]) => meta.user.username)).size

  return (
    <div className="space-y-6">
      {/* 统计概览 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="frosted-card theme-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <div className="p-1.5 rounded-lg theme-gradient">
                <Database className="h-3 w-3 text-white" />
              </div>
              {t("metadata.totalRecords")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold theme-text">{metadataEntries.length}</div>
            <div className="mt-1 h-1 bg-slate-200 dark:bg-slate-700 rounded-full overflow-hidden">
              <div
                className="h-full theme-progress rounded-full"
                style={{ width: `${Math.min(metadataEntries.length * 5, 100)}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>

        <Card className="frosted-card theme-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <div className="p-1.5 rounded-lg theme-gradient">
                <FileText className="h-3 w-3 text-white" />
              </div>
              {t("metadata.models")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold theme-text">{uniqueModels}</div>
            <div className="mt-1 h-1 bg-slate-200 dark:bg-slate-700 rounded-full overflow-hidden">
              <div
                className="h-full theme-progress rounded-full"
                style={{ width: `${Math.min(uniqueModels * 10, 100)}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>

        <Card className="frosted-card theme-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <div className="p-1.5 rounded-lg theme-gradient">
                <User className="h-3 w-3 text-white" />
              </div>
              {t("metadata.users")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold theme-text">{uniqueUsers}</div>
            <div className="mt-1 h-1 bg-slate-200 dark:bg-slate-700 rounded-full overflow-hidden">
              <div
                className="h-full theme-progress rounded-full"
                style={{ width: `${Math.min(uniqueUsers * 15, 100)}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>

        <Card className="frosted-card theme-border">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <div className="p-1.5 rounded-lg theme-gradient">
                <ImageIcon className="h-3 w-3 text-white" />
              </div>
              {t("dashboard.images")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold theme-text">{images.length}</div>
            <div className="mt-1 h-1 bg-slate-200 dark:bg-slate-700 rounded-full overflow-hidden">
              <div
                className="h-full theme-progress rounded-full"
                style={{ width: `${Math.min(images.length * 8, 100)}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 元数据概览 */}
      <Card className="frosted-card theme-border theme-shadow">
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2 theme-text">
            <div className="p-2 rounded-lg theme-gradient">
              <BarChart3 className="h-4 w-4 text-white" />
            </div>
            {t("metadata.overview")}
          </CardTitle>
          <CardDescription>{t("metadata.summary")}</CardDescription>
        </CardHeader>
        <CardContent>
          {metadataEntries.length === 0 ? (
            <div className="text-center py-8">
              <Database className="h-12 w-12 mx-auto text-slate-400 mb-4" />
              <p className="text-sm text-slate-600 dark:text-slate-400">{t("metadata.noMetadata")}</p>
            </div>
          ) : (
            <div className="space-y-4">
              {metadataEntries.slice(0, 5).map(([id, meta]) => (
                <div key={id} className="flex items-center justify-between p-4 rounded-lg theme-accent theme-border">
                  <div className="flex-1">
                    <div className="font-medium text-sm theme-text">{meta.modelInfo.name}</div>
                    <div className="text-xs text-slate-600 dark:text-slate-400">
                      by {meta.user.username} • {meta.imageInfo.width}×{meta.imageInfo.height}
                    </div>
                  </div>
                  <div className="flex gap-1">
                    {meta.modelInfo.tags.slice(0, 2).map((tag) => (
                      <Badge key={tag} className="theme-secondary text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              ))}
              {metadataEntries.length > 5 && (
                <div className="text-center pt-4">
                  <p className="text-xs text-slate-600 dark:text-slate-400 mb-3">
                    {t("metadata.moreRecords").replace("{count}", (metadataEntries.length - 5).toString())}
                  </p>
                  <Button variant="outline" size="sm" className="theme-border theme-ring">
                    <TrendingUp className="mr-2 h-3 w-3" />
                    查看全部
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 数据管理板块 */}
      <Card className="frosted-card theme-border theme-shadow">
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2 theme-text">
            <div className="p-2 rounded-lg theme-gradient">
              <Database className="h-4 w-4 text-white" />
            </div>
            {t("settings.dataManagement")}
          </CardTitle>
          <CardDescription>{t("settings.dataDescription")}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="text-sm font-medium theme-text">{t("settings.exportData")}</h3>
              <p className="text-xs text-slate-600 dark:text-slate-400">{t("settings.exportDescription")}</p>
              <Button onClick={handleExportData} variant="outline" size="sm" className="theme-border theme-ring">
                <Download className="mr-2 h-4 w-4" />
                {t("settings.exportData")}
              </Button>
            </div>

            <div className="space-y-2">
              <h3 className="text-sm font-medium theme-text">{t("settings.clearData")}</h3>
              <p className="text-xs text-slate-600 dark:text-slate-400">{t("settings.clearDescription")}</p>
              <Button
                onClick={() => setShowDeleteDialog(true)}
                variant="outline"
                size="sm"
                className="border-red-200 dark:border-red-800 hover:bg-red-100 dark:hover:bg-red-900/30 text-red-600 dark:text-red-400"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                {t("settings.clearData")}
              </Button>
            </div>
          </div>

          <div className="pt-4 border-t border-white/20 dark:border-white/10">
            <div className="grid grid-cols-2 gap-4 text-center">
              <div>
                <div className="text-lg font-bold theme-text">{images.length}</div>
                <div className="text-xs text-slate-600 dark:text-slate-400">{t("dashboard.images")}</div>
              </div>
              <div>
                <div className="text-lg font-bold theme-text">{Object.keys(metadata).length}</div>
                <div className="text-xs text-slate-600 dark:text-slate-400">{t("dashboard.metadataRecords")}</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 确认删除对话框 */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="frosted-glass">
          <DialogHeader>
            <DialogTitle className="theme-text">{t("settings.clearData")}</DialogTitle>
            <DialogDescription>{t("settings.confirmClear")}</DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex space-x-2 justify-end">
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              {t("common.cancel")}
            </Button>
            <Button variant="destructive" onClick={handleClearData}>
              {t("common.confirm")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
