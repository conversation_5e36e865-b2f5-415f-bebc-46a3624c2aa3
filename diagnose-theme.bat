@echo off

:: =============================================================================
:: Civitai Explorer Theme Diagnosis Script
:: =============================================================================

title Civitai Explorer - Theme Diagnosis

echo.
echo Civitai Explorer Theme Diagnosis
echo =================================
echo.

echo This script applies multiple fixes and adds diagnostic tools:
echo.
echo [FIXES APPLIED]
echo + Added simplified theme test with direct RGB values
echo + Fixed hydration issues with delayed theme application
echo + Added multiple CSS syntax tests (HSL vs direct variables)
echo + Enhanced debugging with real-time monitoring
echo.

:: Clear Next.js cache
echo [STEP 1] Clearing Next.js cache...
if exist ".next" (
    echo Removing .next directory...
    rmdir /s /q ".next"
    echo Done.
) else (
    echo .next directory not found, skipping...
)

echo.
echo [SUCCESS] Diagnostic enhancements applied!
echo.
echo WHAT TO TEST:
echo ------------
echo 1. SIMPLIFIED THEME TEST (NEW):
echo    - Uses direct RGB values instead of CSS variables
echo    - Should work immediately without any CSS issues
echo    - Has its own theme switcher for testing
echo.
echo 2. ORIGINAL THEME TEST (ENHANCED):
echo    - Tests multiple CSS syntax approaches
echo    - HSL wrapper vs direct variable reference
echo    - Hardcoded color test for comparison
echo.
echo 3. CSS VARIABLE DEBUG:
echo    - Real-time variable value monitoring
echo    - Shows exact HSL values being used
echo    - Updates when themes change
echo.
echo DIAGNOSIS STEPS:
echo ---------------
echo Step 1: Test simplified theme (should work)
echo Step 2: Compare with original theme test
echo Step 3: Check CSS variable values in debug section
echo Step 4: Use browser dev tools to inspect computed styles
echo.
echo EXPECTED RESULTS:
echo ----------------
echo - Simplified theme test should work perfectly
echo - Original theme test may still have issues
echo - This will help identify if the problem is:
echo   a) CSS variable scope/syntax
echo   b) Theme application logic
echo   c) Browser compatibility
echo.

pause

echo.
echo Please restart your development server now:
echo 1. Stop current server (Ctrl+C in terminal)
echo 2. Run: quick.bat or start.bat
echo 3. Go to Dashboard page
echo 4. Test BOTH theme sections:
echo    - "Simplified Theme Test" (should work)
echo    - "Theme Test Components" (for comparison)
echo.
echo Report back which tests work and which don't!
echo This will help us identify the exact issue.
echo.

pause
