"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/contexts/language-context"

export function ThemePreview() {
  const { t } = useLanguage()

  return (
    <Card className="backdrop-blur-xl bg-white/20 dark:bg-black/20 border-white/20 dark:border-white/10">
      <CardHeader>
        <CardTitle className="text-slate-900 dark:text-slate-100">{t("theme.preview")}</CardTitle>
        <CardDescription className="text-slate-600 dark:text-slate-400">{t("theme.description")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2 flex-wrap">
          <Button size="sm" className="bg-primary hover:bg-primary/90">
            {t("button.primary")}
          </But<PERSON>>
          <Button variant="outline" size="sm">
            {t("button.secondary")}
          </Button>
          <Button variant="ghost" size="sm">
            {t("button.text")}
          </Button>
        </div>

        <div className="flex gap-2 flex-wrap">
          <Badge className="bg-primary">Primary Tag</Badge>
          <Badge variant="secondary">Secondary Tag</Badge>
          <Badge variant="outline">Outline Tag</Badge>
        </div>

        <div className="space-y-2">
          <div className="h-2 bg-primary rounded-full w-full" />
          <div className="h-2 bg-primary/70 rounded-full w-3/4" />
          <div className="h-2 bg-primary/40 rounded-full w-1/2" />
        </div>

        <p className="text-sm text-slate-700 dark:text-slate-300">{t("theme.text_example")}</p>
      </CardContent>
    </Card>
  )
}
