"use client"

import type React from "react"
import { createContext, useContext, useState } from "react"

type NavigationPage = "dashboard" | "gallery" | "downloader" | "metadata" | "settings"

interface NavigationContextType {
  currentPage: NavigationPage
  setCurrentPage: (page: NavigationPage) => void
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined)

export function useNavigation() {
  const context = useContext(NavigationContext)
  if (context === undefined) {
    throw new Error("useNavigation must be used within a NavigationProvider")
  }
  return context
}

export function NavigationProvider({ children }: { children: React.ReactNode }) {
  const [currentPage, setCurrentPage] = useState<NavigationPage>("dashboard")

  return <NavigationContext.Provider value={{ currentPage, setCurrentPage }}>{children}</NavigationContext.Provider>
}
