"use client"

import * as React from "react"
import { <PERSON>, Sparkles } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu"
import { useThemeTransition } from "@/components/theme-transition-manager"
import { useLanguage } from "@/contexts/language-context"

const themes = [
  {
    name: "默认蓝色",
    value: "default",
    color: "bg-blue-500",
    gradient: "from-blue-400 to-blue-600",
    description: "经典蓝色主题",
    cssVars: {
      "--primary": "221.2 83.2% 53.3%",
      "--ring": "221.2 83.2% 53.3%",
    },
  },
  {
    name: "翡翠绿",
    value: "emerald",
    color: "bg-emerald-500",
    gradient: "from-emerald-400 to-emerald-600",
    description: "清新绿色主题",
    cssVars: {
      "--primary": "160 84% 39%",
      "--ring": "160 84% 39%",
    },
  },
  {
    name: "紫罗兰",
    value: "violet",
    color: "bg-violet-500",
    gradient: "from-violet-400 to-violet-600",
    description: "优雅紫色主题",
    cssVars: {
      "--primary": "262 83% 58%",
      "--ring": "262 83% 58%",
    },
  },
  {
    name: "玫瑰红",
    value: "rose",
    color: "bg-rose-500",
    gradient: "from-rose-400 to-rose-600",
    description: "温暖红色主题",
    cssVars: {
      "--primary": "346 77% 50%",
      "--ring": "346 77% 50%",
    },
  },
  {
    name: "琥珀橙",
    value: "amber",
    color: "bg-amber-500",
    gradient: "from-amber-400 to-amber-600",
    description: "活力橙色主题",
    cssVars: {
      "--primary": "45 93% 47%",
      "--ring": "45 93% 47%",
    },
  },
  {
    name: "青色",
    value: "cyan",
    color: "bg-cyan-500",
    gradient: "from-cyan-400 to-cyan-600",
    description: "清爽青色主题",
    cssVars: {
      "--primary": "188 94% 43%",
      "--ring": "188 94% 43%",
    },
  },
  {
    name: "粉色",
    value: "pink",
    color: "bg-pink-500",
    gradient: "from-pink-400 to-pink-600",
    description: "甜美粉色主题",
    cssVars: {
      "--primary": "330 81% 60%",
      "--ring": "330 81% 60%",
    },
  },
  {
    name: "靛蓝",
    value: "indigo",
    color: "bg-indigo-500",
    gradient: "from-indigo-400 to-indigo-600",
    description: "深邃靛蓝主题",
    cssVars: {
      "--primary": "239 84% 67%",
      "--ring": "239 84% 67%",
    },
  },
  {
    name: "石板灰",
    value: "slate",
    color: "bg-slate-500",
    gradient: "from-slate-400 to-slate-600",
    description: "专业灰色主题",
    cssVars: {
      "--primary": "215 28% 17%",
      "--ring": "215 28% 17%",
    },
  },
  {
    name: "中性灰",
    value: "neutral",
    color: "bg-neutral-500",
    gradient: "from-neutral-400 to-neutral-600",
    description: "简约中性主题",
    cssVars: {
      "--primary": "0 0% 9%",
      "--ring": "0 0% 9%",
    },
  },
]

export function ThemeSelector() {
  const [currentTheme, setCurrentTheme] = React.useState("default")
  const [mounted, setMounted] = React.useState(false)
  const { startTransition, isTransitioning } = useThemeTransition()
  const { t } = useLanguage()

  const themes = [
    {
      name: t("color.default"),
      value: "default",
      color: "bg-blue-500",
      gradient: "from-blue-400 to-blue-600",
      description: t("theme.classicBlue"),
      cssVars: {
        "--primary": "221.2 83.2% 53.3%",
        "--ring": "221.2 83.2% 53.3%",
      },
    },
    {
      name: t("color.emerald"),
      value: "emerald",
      color: "bg-emerald-500",
      gradient: "from-emerald-400 to-emerald-600",
      description: t("theme.freshGreen"),
      cssVars: {
        "--primary": "160 84% 39%",
        "--ring": "160 84% 39%",
      },
    },
    {
      name: t("color.violet"),
      value: "violet",
      color: "bg-violet-500",
      gradient: "from-violet-400 to-violet-600",
      description: t("theme.elegantPurple"),
      cssVars: {
        "--primary": "262 83% 58%",
        "--ring": "262 83% 58%",
      },
    },
    {
      name: t("color.rose"),
      value: "rose",
      color: "bg-rose-500",
      gradient: "from-rose-400 to-rose-600",
      description: t("theme.warmRed"),
      cssVars: {
        "--primary": "346 77% 50%",
        "--ring": "346 77% 50%",
      },
    },
    {
      name: t("color.amber"),
      value: "amber",
      color: "bg-amber-500",
      gradient: "from-amber-400 to-amber-600",
      description: t("theme.vibrantOrange"),
      cssVars: {
        "--primary": "45 93% 47%",
        "--ring": "45 93% 47%",
      },
    },
    {
      name: t("color.cyan"),
      value: "cyan",
      color: "bg-cyan-500",
      gradient: "from-cyan-400 to-cyan-600",
      description: t("theme.coolCyan"),
      cssVars: {
        "--primary": "188 94% 43%",
        "--ring": "188 94% 43%",
      },
    },
    {
      name: t("color.pink"),
      value: "pink",
      color: "bg-pink-500",
      gradient: "from-pink-400 to-pink-600",
      description: t("theme.sweetPink"),
      cssVars: {
        "--primary": "330 81% 60%",
        "--ring": "330 81% 60%",
      },
    },
    {
      name: t("color.indigo"),
      value: "indigo",
      color: "bg-indigo-500",
      gradient: "from-indigo-400 to-indigo-600",
      description: t("theme.deepIndigo"),
      cssVars: {
        "--primary": "239 84% 67%",
        "--ring": "239 84% 67%",
      },
    },
    {
      name: t("color.slate"),
      value: "slate",
      color: "bg-slate-500",
      gradient: "from-slate-400 to-slate-600",
      description: t("theme.professionalGray"),
      cssVars: {
        "--primary": "215 28% 17%",
        "--ring": "215 28% 17%",
      },
    },
    {
      name: t("color.neutral"),
      value: "neutral",
      color: "bg-neutral-500",
      gradient: "from-neutral-400 to-neutral-600",
      description: t("theme.minimalNeutral"),
      cssVars: {
        "--primary": "0 0% 9%",
        "--ring": "0 0% 9%",
      },
    },
  ]

  React.useEffect(() => {
    setMounted(true)
    const savedTheme = localStorage.getItem("app-color-theme") || "default"
    setCurrentTheme(savedTheme)
    applyTheme(savedTheme, false)
  }, [])

  const applyTheme = async (themeValue: string, withTransition = true) => {
    if (isTransitioning) return

    console.log("Applying theme:", themeValue)

    if (withTransition) {
      await startTransition()
    }

    const theme = themes.find((t) => t.value === themeValue)
    if (!theme) return

    const root = document.documentElement

    // Remove all theme classes
    themes.forEach((t) => {
      root.classList.remove(`theme-${t.value}`)
    })

    // Apply new theme class
    root.classList.add(`theme-${themeValue}`)
    root.setAttribute("data-color-theme", themeValue)

    // Apply CSS custom properties
    Object.entries(theme.cssVars).forEach(([property, value]) => {
      root.style.setProperty(property, value)
    })

    // Also update Tailwind CSS variables for better integration
    const themeVars = {
      "--primary": theme.cssVars["--theme-primary"] || theme.cssVars["--primary"],
      "--primary-foreground": theme.cssVars["--theme-primary-foreground"],
      "--secondary": theme.cssVars["--theme-secondary"],
      "--secondary-foreground": theme.cssVars["--theme-secondary-foreground"],
      "--accent": theme.cssVars["--theme-accent"],
      "--accent-foreground": theme.cssVars["--theme-accent-foreground"],
      "--ring": theme.cssVars["--theme-primary"] || theme.cssVars["--primary"],
    }

    Object.entries(themeVars).forEach(([property, value]) => {
      if (value) {
        root.style.setProperty(property, value)
      }
    })

    // Save to localStorage
    localStorage.setItem("app-color-theme", themeValue)
    setCurrentTheme(themeValue)

    // Add transition effect
    root.classList.add("theme-transitioning")
    setTimeout(() => {
      root.classList.remove("theme-transitioning")
    }, 500)

    console.log("Theme applied successfully:", themeValue)
  }

  const getCurrentTheme = () => {
    return themes.find((theme) => theme.value === currentTheme) || themes[0]
  }

  if (!mounted) {
    return (
      <div className="flex items-center justify-between w-full text-left p-1">
        <span className="text-sm">{t("theme.colorTheme")}</span>
        <div className="h-4 w-4 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 ring-2 ring-white/20" />
      </div>
    )
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="flex items-center justify-between w-full text-left hover:bg-white/10 dark:hover:bg-white/5 rounded-md transition-colors p-1 cursor-pointer">
          <span className="text-sm">{t("theme.colorTheme")}</span>
          <div
            className={`h-4 w-4 rounded-full bg-gradient-to-r ${getCurrentTheme().gradient} ring-2 ring-white/20 transition-transform hover:scale-110`}
          />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="start"
        className="w-64 frosted-glass border-white/10 dark:border-white/5 z-[100] backdrop-blur-xl bg-white/20 dark:bg-black/20"
        sideOffset={5}
      >
        <DropdownMenuLabel className="text-slate-900 dark:text-slate-100 flex items-center gap-2">
          <Sparkles className="h-4 w-4" />
          Select Color Theme
        </DropdownMenuLabel>
        <DropdownMenuSeparator className="bg-white/20 dark:bg-white/10" />
        {themes.map((theme, index) => (
          <DropdownMenuItem
            key={theme.value}
            onClick={() => applyTheme(theme.value)}
            disabled={isTransitioning}
            className="cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed hover:bg-white/10 dark:hover:bg-white/5 transition-all duration-200"
            style={{
              animationDelay: `${index * 0.03}s`,
            }}
          >
            <div className="flex items-center gap-3 w-full">
              <div
                className={`h-5 w-5 rounded-full bg-gradient-to-r ${theme.gradient} flex-shrink-0 ring-2 ring-white/20 transition-all duration-200 ${
                  currentTheme === theme.value ? "scale-110 ring-white/40" : "hover:scale-105"
                }`}
              />
              <div className="flex-1">
                <div className="font-medium text-slate-900 dark:text-slate-100 text-sm">{theme.name}</div>
                <div className="text-xs text-slate-600 dark:text-slate-400">{theme.description}</div>
              </div>
              {currentTheme === theme.value && (
                <Check className="h-4 w-4 text-slate-900 dark:text-slate-100 animate-in fade-in-0 zoom-in-95 duration-200" />
              )}
            </div>
          </DropdownMenuItem>
        ))}
        {isTransitioning && (
          <div className="absolute inset-0 glass-effect flex items-center justify-center backdrop-blur-sm">
            <div className="animate-spin rounded-full h-6 w-6 border-2 border-primary border-t-transparent"></div>
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
