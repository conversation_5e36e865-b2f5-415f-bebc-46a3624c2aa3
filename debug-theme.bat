@echo off

:: =============================================================================
:: Civitai Explorer Theme Debug Script
:: =============================================================================

title Civitai Explorer - Theme Debug

echo.
echo Civitai Explorer Theme Debug
echo ============================
echo.

echo This script will restart the server with enhanced theme debugging:
echo.
echo [DEBUGGING ENHANCEMENTS]
echo + Added !important to all theme CSS rules for priority
echo + Added theme variable debug display
echo + Added inline style tests to verify CSS variables
echo + Enhanced theme test components
echo.

:: Clear Next.js cache
echo [STEP 1] Clearing Next.js cache...
if exist ".next" (
    echo Removing .next directory...
    rmdir /s /q ".next"
    echo Done.
) else (
    echo .next directory not found, skipping...
)

echo.
echo [SUCCESS] Theme debugging enhancements ready!
echo.
echo WHAT TO CHECK:
echo -------------
echo 1. THEME VARIABLE DEBUG:
echo    - Look for "Theme Variable Debug Info" section
echo    - Check if CSS variables show actual HSL values
echo    - Variables should change when switching themes
echo.
echo 2. INLINE STYLE TESTS:
echo    - "Inline Theme Button" and "Inline Outline Button"
echo    - These use direct CSS variable references
echo    - Should work if variables are properly set
echo.
echo 3. CSS CLASS TESTS:
echo    - All theme-* classes now use !important
echo    - Should override any conflicting styles
echo    - Look for visual changes in theme components
echo.
echo TROUBLESHOOTING:
echo ---------------
echo If theme colors still don't work:
echo 1. Check browser developer tools
echo 2. Inspect element styles
echo 3. Look for CSS variable values in computed styles
echo 4. Check console for any JavaScript errors
echo.

pause

echo.
echo Please restart your development server now:
echo 1. Stop current server (Ctrl+C in terminal)
echo 2. Run: quick.bat or start.bat
echo 3. Go to Dashboard and scroll to theme test section
echo 4. Check the debug information and test buttons
echo.
echo If inline style buttons work but CSS classes don't,
echo there may be a CSS specificity or loading issue.
echo.

pause
