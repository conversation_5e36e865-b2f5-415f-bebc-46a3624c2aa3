"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ThemeSettingsPanel } from "@/components/theme-settings-panel"
import { Palette } from "lucide-react"
import { AnimationSettings } from "@/components/animation-settings"
import { useThemeTransition } from "@/components/theme-transition-manager"
import { LanguageSettings } from "@/components/language-settings"
import { ApiKeySettings } from "@/components/civitai/api-key-settings"
import { useLanguage } from "@/contexts/language-context"

export function SettingsPage() {
  const { currentAnimation, setCurrentAnimation } = useThemeTransition()
  const { t } = useLanguage()

  return (
    <div className="space-y-4">
      <Card className="frosted-card">
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Palette className="h-4 w-4" />
            {t("settings.themeSettings")}
          </CardTitle>
          <CardDescription>{t("settings.themeDescription")}</CardDescription>
        </CardHeader>
        <CardContent>
          <ThemeSettingsPanel />
        </CardContent>
      </Card>

      {/* 添加动画设置 */}
      <AnimationSettings currentAnimation={currentAnimation} onAnimationChange={setCurrentAnimation} />

      {/* 添加语言设置 */}
      <LanguageSettings />

      {/* 添加API密钥设置 */}
      <ApiKeySettings />

      <Card className="frosted-card">
        <CardHeader>
          <CardTitle className="text-base">{t("settings.appInfo")}</CardTitle>
          <CardDescription>{t("settings.appInfoDescription")}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>{t("settings.version")}:</span>
              <span>1.0.0</span>
            </div>
            <div className="flex justify-between">
              <span>{t("settings.framework")}:</span>
              <span>React + Next.js</span>
            </div>
            <div className="flex justify-between">
              <span>{t("settings.designSystem")}:</span>
              <span>Frosted Glass UI</span>
            </div>
            <div className="flex justify-between">
              <span>{t("settings.resolution")}:</span>
              <span>1200×860px</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
