"use client"

import { Home, ImageIcon, Download, Database, <PERSON>tings, Moon, Sun, <PERSON>lette, <PERSON><PERSON><PERSON> } from "lucide-react"
import { useTheme } from "next-themes"
import { Separator } from "@/components/ui/separator"
import { ThemeSelector } from "@/components/theme-selector"
import { useThemeTransition } from "@/components/theme-transition-manager"
import { useNavigation } from "@/components/navigation-provider"
import { useEffect, useState } from "react"
import { useLanguage } from "@/contexts/language-context"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  useSidebar,
} from "@/components/ui/sidebar"

export function AppSidebar() {
  const { theme, setTheme } = useTheme()
  const { startTransition } = useThemeTransition()
  const { currentPage, setCurrentPage } = useNavigation()
  const [mounted, setMounted] = useState(false)
  const { t } = useLanguage()
  const { state } = useSidebar()

  useEffect(() => {
    setMounted(true)
  }, [])

  const handleThemeToggle = async () => {
    console.log("Theme toggle clicked, current theme:", theme)

    try {
      await startTransition()

      const currentTheme = document.documentElement.classList.contains("dark") ? "dark" : "light"
      const newTheme = currentTheme === "dark" ? "light" : "dark"

      console.log("Current actual theme:", currentTheme, "Setting new theme:", newTheme)

      document.documentElement.classList.remove("light", "dark")
      document.documentElement.classList.add(newTheme)
      document.documentElement.setAttribute("data-theme", newTheme)

      setTheme(newTheme)

      console.log("Theme changed to:", newTheme)
    } catch (error) {
      console.error("Error changing theme:", error)
    }
  }

  const handleNavigation = (pageId: (typeof navigationItems)[0]["id"]) => {
    setCurrentPage(pageId)
  }

  if (!mounted) {
    return null
  }

  const navigationItems = [
    {
      id: "dashboard" as const,
      title: t("nav.dashboard"),
      icon: Home,
      gradient: "from-blue-500 to-indigo-600",
    },
    {
      id: "gallery" as const,
      title: t("nav.gallery"),
      icon: ImageIcon,
      gradient: "from-purple-500 to-pink-600",
    },
    {
      id: "downloader" as const,
      title: t("nav.downloader"),
      icon: Download,
      gradient: "from-green-500 to-emerald-600",
    },
    {
      id: "metadata" as const,
      title: t("nav.metadata"),
      icon: Database,
      gradient: "from-orange-500 to-red-600",
    },
    {
      id: "settings" as const,
      title: t("nav.settings"),
      icon: Settings,
      gradient: "from-gray-500 to-slate-600",
    },
  ]

  return (
    <Sidebar collapsible="icon" className="border-r-0">
      <SidebarHeader className="border-b border-white/20 dark:border-white/10 bg-white/20 dark:bg-black/20 backdrop-blur-xl">
        <div className={`flex items-center gap-3 p-2 ${state === "collapsed" ? "justify-center" : ""}`}>
          <div className="relative overflow-hidden rounded-xl p-2 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 flex-shrink-0">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-pink-400/20 animate-pulse"></div>
            <ImageIcon className="h-6 w-6 text-white relative z-10" />
          </div>
          {state === "expanded" && (
            <div className="flex flex-col min-w-0">
              <span className="text-sm font-bold text-slate-900 dark:text-slate-100 flex items-center gap-1 truncate">
                {t("app.title")}
                <Sparkles className="h-3 w-3 text-blue-500 animate-pulse flex-shrink-0" />
              </span>
              <span className="text-xs text-slate-600 dark:text-slate-400 truncate">{t("app.subtitle")}</span>
            </div>
          )}
        </div>
      </SidebarHeader>

      <SidebarContent className="bg-white/20 dark:bg-black/20 backdrop-blur-xl">
        <SidebarGroup>
          {state === "expanded" && (
            <SidebarGroupLabel className="text-xs font-medium text-slate-700 dark:text-slate-300 flex items-center gap-2">
              <div className="h-1 w-1 rounded-full bg-blue-500"></div>
              {t("nav.navigation")}
            </SidebarGroupLabel>
          )}
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationItems.map((item) => (
                <SidebarMenuItem key={item.id}>
                  <SidebarMenuButton
                    onClick={() => handleNavigation(item.id)}
                    isActive={currentPage === item.id}
                    tooltip={state === "collapsed" ? item.title : undefined}
                    className={`group relative overflow-hidden ${
                      state === "collapsed" ? "justify-center px-2" : "justify-start"
                    }`}
                  >
                    <div
                      className={`relative overflow-hidden rounded-lg p-1.5 bg-gradient-to-r ${item.gradient} transition-all duration-300 group-hover:scale-110 flex-shrink-0 ${
                        currentPage === item.id ? "scale-105 shadow-lg" : ""
                      }`}
                    >
                      <item.icon className="h-4 w-4 text-white transition-transform duration-300 group-hover:rotate-12" />
                      {currentPage === item.id && (
                        <div className="absolute inset-0 bg-white/20 animate-pulse rounded-lg"></div>
                      )}
                    </div>
                    {state === "expanded" && (
                      <>
                        <span className="transition-colors duration-200 ml-2">{item.title}</span>
                        {currentPage === item.id && (
                          <div className="ml-auto h-2 w-2 rounded-full bg-blue-500 animate-pulse"></div>
                        )}
                      </>
                    )}
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <Separator className="bg-gradient-to-r from-transparent via-white/20 to-transparent dark:via-white/10 my-4" />

        <SidebarGroup>
          {state === "expanded" && (
            <SidebarGroupLabel className="text-xs font-medium text-slate-700 dark:text-slate-300 flex items-center gap-2">
              <div className="h-1 w-1 rounded-full bg-purple-500"></div>
              {t("nav.appearance")}
            </SidebarGroupLabel>
          )}
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton
                  tooltip={state === "collapsed" ? t("theme.colorTheme") : undefined}
                  className={`group relative overflow-hidden ${
                    state === "collapsed" ? "justify-center px-2" : "justify-start"
                  }`}
                >
                  <div className="relative overflow-hidden rounded-lg p-1.5 bg-gradient-to-r from-pink-500 to-violet-500 transition-all duration-300 group-hover:scale-110 flex-shrink-0">
                    <Palette className="h-4 w-4 text-white transition-transform duration-300 group-hover:rotate-12" />
                  </div>
                  {state === "expanded" && (
                    <div className="flex-1 ml-2">
                      <ThemeSelector />
                    </div>
                  )}
                </SidebarMenuButton>
              </SidebarMenuItem>

              <SidebarMenuItem>
                <SidebarMenuButton
                  onClick={handleThemeToggle}
                  tooltip={
                    state === "collapsed" ? (theme === "dark" ? t("theme.lightMode") : t("theme.darkMode")) : undefined
                  }
                  className={`group relative overflow-hidden ${
                    state === "collapsed" ? "justify-center px-2" : "justify-start"
                  }`}
                >
                  <div className="relative overflow-hidden rounded-lg p-1.5 bg-gradient-to-r from-yellow-400 to-orange-500 transition-all duration-300 group-hover:scale-110 flex-shrink-0">
                    {theme === "dark" ? (
                      <Sun className="h-4 w-4 text-white transition-transform duration-500 group-hover:rotate-180" />
                    ) : (
                      <Moon className="h-4 w-4 text-white transition-transform duration-500 group-hover:-rotate-12" />
                    )}
                    <div className="absolute inset-0 bg-gradient-to-r from-yellow-300/30 to-orange-400/30 animate-pulse rounded-lg"></div>
                  </div>
                  {state === "expanded" && (
                    <span className="transition-colors duration-200 ml-2">
                      {theme === "dark" ? t("theme.lightMode") : t("theme.darkMode")}
                    </span>
                  )}
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="border-t border-white/20 dark:border-white/10 bg-white/20 dark:bg-black/20 backdrop-blur-xl">
        {state === "expanded" && (
          <div className="text-center space-y-1 p-2">
            <div className="text-xs font-medium text-slate-600 dark:text-slate-400 flex items-center justify-center gap-1">
              Civitai Explorer
              <div className="h-1 w-1 rounded-full bg-blue-500 animate-pulse"></div>
            </div>
            <div className="text-xs opacity-75 text-slate-500 dark:text-slate-500">{t("app.version")}</div>
          </div>
        )}
        {state === "collapsed" && (
          <div className="flex justify-center p-2">
            <div className="h-2 w-2 rounded-full bg-blue-500 animate-pulse"></div>
          </div>
        )}
      </SidebarFooter>

      <SidebarRail />
    </Sidebar>
  )
}
