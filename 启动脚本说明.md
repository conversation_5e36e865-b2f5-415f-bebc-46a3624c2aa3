# 🚀 Civitai Explorer 启动脚本说明

本项目提供了多个启动脚本，方便您快速启动和管理 Civitai Explorer 应用。

## 📁 脚本文件

### 1. `start.bat` - 完整功能启动器 ⭐ **推荐使用**
**功能最全面的批处理脚本，已修复编码问题**

**特性：**
- 🎯 交互式菜单界面（英文界面，避免编码问题）
- 🔍 自动检测 Node.js 和包管理器
- 📦 智能依赖管理
- 🚀 一键启动开发服务器
- 🏗️ 生产环境构建和部署
- 🧹 缓存清理和重新安装
- 📊 项目信息展示
- 🌍 自动打开浏览器

**使用方法：**
```bash
# 双击运行或在命令行中执行
start.bat
```

### 2. `quick.bat` - 快速启动脚本 ⚡ **日常推荐**
**简化版本** - 适合日常快速启动

**特性：**
- ⚡ 极简操作，一键启动
- 📦 自动安装依赖（首次运行）
- 🌐 自动打开浏览器
- 🔧 智能包管理器检测
- 🛡️ 无编码问题，纯英文界面

**使用方法：**
```bash
# 双击运行或在命令行中执行
quick.bat
```

### 3. `start-project.ps1` - PowerShell 版本
**PowerShell 用户专用** - 功能与完整版相同

**特性：**
- 🎨 彩色输出界面
- 🔧 PowerShell 原生支持
- 📋 完整的项目管理功能
- 🛡️ 更好的错误处理

**使用方法：**
```powershell
# 在 PowerShell 中执行
.\start-project.ps1

# 或者右键选择"使用 PowerShell 运行"
```

## 🎯 功能说明

### 主要功能
1. **🚀 快速启动开发服务器**
   - 自动检测并安装依赖
   - 启动 Next.js 开发服务器
   - 自动打开浏览器到 http://localhost:3000

2. **📦 依赖管理**
   - 优先使用 pnpm，fallback 到 npm
   - 智能检测包管理器
   - 支持依赖更新和重新安装

3. **🏗️ 生产环境支持**
   - 构建优化的生产版本
   - 启动生产服务器
   - 性能优化检查

4. **🔧 开发工具**
   - ESLint 代码检查
   - 缓存清理
   - 项目信息展示

## 🛠️ 系统要求

### 必需环境
- **Node.js** 18.0.0 或更高版本
- **Windows** 操作系统
- **包管理器**: npm (内置) 或 pnpm (推荐)

### 推荐配置
- **pnpm** - 更快的包管理器
- **现代浏览器** - Chrome, Firefox, Edge
- **终端** - Windows Terminal (推荐)

## 🚀 快速开始

### 首次使用
1. 确保已安装 Node.js
2. 下载项目到本地
3. 双击 `start.bat` 或 `quick.bat`
4. 选择 "Quick Start Dev Server" (对于 start.bat)
5. 等待自动打开浏览器

### 日常开发
- 使用 `quick.bat` 快速启动（推荐）
- 或使用 `start.bat` 进行完整管理

## ⚠️ 重要说明

**编码问题修复：**
- 原来的 `start-project.bat` 和 `quick-start.bat` 可能在某些 Windows 系统上出现中文编码问题
- 新的 `start.bat` 和 `quick.bat` 使用纯英文界面，完全避免编码问题
- 功能完全相同，只是界面语言改为英文

## 🔧 故障排除

### 常见问题

**1. "未找到 Node.js"**
```bash
# 解决方案：安装 Node.js
# 访问 https://nodejs.org/ 下载安装
```

**2. "依赖安装失败"**
```bash
# 解决方案：清理缓存重新安装
# 在启动脚本中选择 "清理缓存和重新安装"
```

**3. "端口 3000 被占用"**
```bash
# 解决方案：关闭占用端口的程序
# 或修改 package.json 中的端口配置
```

**4. "PowerShell 执行策略限制"**
```powershell
# 解决方案：临时允许脚本执行
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 手动启动方式
如果脚本无法正常工作，可以手动执行：

```bash
# 安装依赖
npm install
# 或
pnpm install

# 启动开发服务器
npm run dev
# 或
pnpm dev
```

## 📝 自定义配置

### 修改默认端口
编辑 `package.json` 文件：
```json
{
  "scripts": {
    "dev": "next dev -p 3001"
  }
}
```

### 添加环境变量
创建 `.env.local` 文件：
```env
NEXT_PUBLIC_API_URL=your_api_url
```

## 🎨 界面预览

启动后您将看到：
- 🎯 现代化的侧边栏界面
- 🌈 多主题色彩系统
- 🔍 图像浏览和管理功能
- 📊 数据统计仪表板
- ⚙️ 设置和配置面板

## 📞 技术支持

如果遇到问题：
1. 检查 Node.js 版本是否符合要求
2. 确保网络连接正常
3. 查看控制台错误信息
4. 尝试清理缓存重新安装

## 🎉 开始使用

现在您可以：
1. 双击 `start-project.bat` 开始使用
2. 或者使用 `quick-start.bat` 快速启动
3. 享受 Civitai Explorer 的强大功能！

---

**大叔，我们一起加油！** 🚀
