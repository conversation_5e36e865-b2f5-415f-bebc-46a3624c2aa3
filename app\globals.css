@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;

    /* Sidebar Variables */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Enhanced Frosted Glass Variables */
    --glass-opacity: 0.75;
    --glass-blur: 10px;
    --glass-saturation: 150%;
    --glass-brightness: 1.05;
    --glass-border-opacity: 0.25;
    --glass-shadow-opacity: 0.12;

    /* Animation Variables */
    --theme-transition-duration: 0.5s;
    --theme-transition-easing: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --hover-transition-duration: 0.15s;

    /* Theme Color Variables - Default Blue */
    --theme-primary: 221.2 83.2% 53.3%;
    --theme-primary-foreground: 210 40% 98%;
    --theme-secondary: 221.2 83.2% 93.3%;
    --theme-secondary-foreground: 221.2 83.2% 23.3%;
    --theme-accent: 221.2 83.2% 88.3%;
    --theme-accent-foreground: 221.2 83.2% 18.3%;
    --theme-gradient-from: 221 83% 53%;
    --theme-gradient-to: 239 84% 67%;
    --theme-shadow: 221 83% 53%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;

    /* Dark Mode Sidebar Variables */
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 0 0% 98%;
    --sidebar-primary-foreground: 240 5.9% 10%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Dark Mode Frosted Glass Variables */
    --glass-opacity: 0.6;
    --glass-blur: 12px;
    --glass-saturation: 120%;
    --glass-brightness: 0.95;
    --glass-border-opacity: 0.2;
    --glass-shadow-opacity: 0.15;
  }

  /* Theme Color Variants */
  .theme-emerald {
    --primary: 160 84% 39%;
    --ring: 160 84% 39%;
    --theme-primary: 160 84% 39%;
    --theme-primary-foreground: 210 40% 98%;
    --theme-secondary: 160 84% 89%;
    --theme-secondary-foreground: 160 84% 19%;
    --theme-accent: 160 84% 84%;
    --theme-accent-foreground: 160 84% 14%;
    --theme-gradient-from: 160 84% 39%;
    --theme-gradient-to: 158 64% 52%;
    --theme-shadow: 160 84% 39%;
  }

  .theme-violet {
    --primary: 262 83% 58%;
    --ring: 262 83% 58%;
    --theme-primary: 262 83% 58%;
    --theme-primary-foreground: 210 40% 98%;
    --theme-secondary: 262 83% 93%;
    --theme-secondary-foreground: 262 83% 28%;
    --theme-accent: 262 83% 88%;
    --theme-accent-foreground: 262 83% 18%;
    --theme-gradient-from: 262 83% 58%;
    --theme-gradient-to: 263 70% 50%;
    --theme-shadow: 262 83% 58%;
  }

  .theme-rose {
    --primary: 346 77% 50%;
    --ring: 346 77% 50%;
    --theme-primary: 346 77% 50%;
    --theme-primary-foreground: 210 40% 98%;
    --theme-secondary: 346 77% 90%;
    --theme-secondary-foreground: 346 77% 20%;
    --theme-accent: 346 77% 85%;
    --theme-accent-foreground: 346 77% 15%;
    --theme-gradient-from: 346 77% 50%;
    --theme-gradient-to: 350 89% 60%;
    --theme-shadow: 346 77% 50%;
  }

  .theme-amber {
    --primary: 45 93% 47%;
    --ring: 45 93% 47%;
    --theme-primary: 45 93% 47%;
    --theme-primary-foreground: 26 83% 14%;
    --theme-secondary: 45 93% 87%;
    --theme-secondary-foreground: 45 93% 17%;
    --theme-accent: 45 93% 82%;
    --theme-accent-foreground: 45 93% 12%;
    --theme-gradient-from: 45 93% 47%;
    --theme-gradient-to: 43 96% 56%;
    --theme-shadow: 45 93% 47%;
  }

  .theme-cyan {
    --primary: 188 94% 43%;
    --ring: 188 94% 43%;
    --theme-primary: 188 94% 43%;
    --theme-primary-foreground: 210 40% 98%;
    --theme-secondary: 188 94% 88%;
    --theme-secondary-foreground: 188 94% 18%;
    --theme-accent: 188 94% 83%;
    --theme-accent-foreground: 188 94% 13%;
    --theme-gradient-from: 188 94% 43%;
    --theme-gradient-to: 186 83% 53%;
    --theme-shadow: 188 94% 43%;
  }

  .theme-pink {
    --primary: 330 81% 60%;
    --ring: 330 81% 60%;
    --theme-primary: 330 81% 60%;
    --theme-primary-foreground: 210 40% 98%;
    --theme-secondary: 330 81% 95%;
    --theme-secondary-foreground: 330 81% 30%;
    --theme-accent: 330 81% 90%;
    --theme-accent-foreground: 330 81% 20%;
    --theme-gradient-from: 330 81% 60%;
    --theme-gradient-to: 333 71% 51%;
    --theme-shadow: 330 81% 60%;
  }

  .theme-indigo {
    --primary: 239 84% 67%;
    --ring: 239 84% 67%;
    --theme-primary: 239 84% 67%;
    --theme-primary-foreground: 210 40% 98%;
    --theme-secondary: 239 84% 92%;
    --theme-secondary-foreground: 239 84% 37%;
    --theme-accent: 239 84% 87%;
    --theme-accent-foreground: 239 84% 27%;
    --theme-gradient-from: 239 84% 67%;
    --theme-gradient-to: 238 75% 59%;
    --theme-shadow: 239 84% 67%;
  }

  .theme-slate {
    --primary: 215 28% 17%;
    --ring: 215 28% 17%;
    --theme-primary: 215 28% 17%;
    --theme-primary-foreground: 210 40% 98%;
    --theme-secondary: 215 28% 87%;
    --theme-secondary-foreground: 215 28% 17%;
    --theme-accent: 215 28% 82%;
    --theme-accent-foreground: 215 28% 12%;
    --theme-gradient-from: 215 28% 17%;
    --theme-gradient-to: 215 25% 27%;
    --theme-shadow: 215 28% 17%;
  }

  .theme-neutral {
    --primary: 0 0% 9%;
    --ring: 0 0% 9%;
    --theme-primary: 0 0% 9%;
    --theme-primary-foreground: 0 0% 98%;
    --theme-secondary: 0 0% 89%;
    --theme-secondary-foreground: 0 0% 9%;
    --theme-accent: 0 0% 84%;
    --theme-accent-foreground: 0 0% 4%;
    --theme-gradient-from: 0 0% 9%;
    --theme-gradient-to: 0 0% 19%;
    --theme-shadow: 0 0% 9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    margin: 0;
    padding: 0;
    font-size: 14px;
    transition: background-color var(--theme-transition-duration) var(--theme-transition-easing), color
      var(--theme-transition-duration) var(--theme-transition-easing);
  }
  html {
    height: 100%;
    transition: background-color var(--theme-transition-duration) var(--theme-transition-easing);
  }
}

/* App Container - Full Screen */
body {
  background: linear-gradient(
    135deg,
    rgb(248 250 252) 0%,
    rgb(239 246 255) 25%,
    rgb(224 242 254) 50%,
    rgb(199 210 254) 100%
  );
  transition: background var(--theme-transition-duration) var(--theme-transition-easing);
}

.dark body {
  background: linear-gradient(135deg, rgb(15 23 42) 0%, rgb(30 41 59) 25%, rgb(51 65 85) 50%, rgb(67 56 202) 100%);
}

/* Theme Transition Animations */
.theme-transitioning,
.theme-transitioning * {\
  transition: background-color var(--theme-transition-duration) var(--theme-transition-easing) !important,
              border-color var(--theme-transition-duration) var(--theme-transition-easing) !important,
              color var(--theme-transition-duration) var(--theme-transition-easing) !important,
              backdrop-filter var(--theme-transition-duration) var(--theme-transition-easing) !important;
}

/* Frosted Glass Effects */
.frosted-glass {
  background-color: rgba(255, 255, 255, var(--glass-opacity));
  backdrop-filter: blur(var(--glass-blur)) saturate(var(--glass-saturation)) brightness(var(--glass-brightness));
  -webkit-backdrop-filter: blur(var(--glass-blur)) saturate(var(--glass-saturation)) brightness(var(--glass-brightness));
  border: 1px solid rgba(255, 255, 255, var(--glass-border-opacity));
  box-shadow: 0 8px 32px rgba(31, 38, 135, var(--glass-shadow-opacity));
  transition: all var(--theme-transition-duration) var(--theme-transition-easing);
}

.dark .frosted-glass {
  background-color: rgba(15, 23, 42, var(--glass-opacity));
  border-color: rgba(255, 255, 255, var(--glass-border-opacity));
}

.frosted-card {
  @apply frosted-glass rounded-lg;
}

.frosted-glass-hover {
  @apply frosted-glass hover:bg-white/30 dark:hover:bg-white/20;
}

/* Theme-aware components */
.theme-primary {
  background-color: hsl(var(--theme-primary));
  color: hsl(var(--theme-primary-foreground));
}

.theme-primary-hover:hover {
  background-color: hsl(var(--theme-primary) / 0.9);
}

.theme-secondary {
  background-color: hsl(var(--theme-secondary));
  color: hsl(var(--theme-secondary-foreground));
}

.theme-accent {
  background-color: hsl(var(--theme-accent));
  color: hsl(var(--theme-accent-foreground));
}

.theme-border {
  border-color: hsl(var(--theme-primary) / 0.3);
}

.theme-text {
  color: hsl(var(--theme-primary));
}

.theme-gradient {
  background: linear-gradient(135deg, hsl(var(--theme-gradient-from)), hsl(var(--theme-gradient-to)));
}

.theme-shadow {
  box-shadow: 0 4px 14px 0 hsl(var(--theme-shadow) / 0.2);
}

.theme-ring {
  --tw-ring-color: hsl(var(--theme-primary) / 0.5);
}

/* Progress bars */
.theme-progress {
  background-color: hsl(var(--theme-primary));
}

.theme-progress.theme-gradient {
  background: linear-gradient(90deg, 
    hsl(var(--theme-gradient-from)), 
    hsl(var(--theme-gradient-to))
  );
  background-size: 200% 100%;
  animation: gradientMove 2s linear infinite;
}

@keyframes gradientMove {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 200% 0%;
  }
}

/* 进度条动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Image Grid */
.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
}

.image-card {
  position: relative;
  overflow: hidden;
  border-radius: 0.75rem;
  aspect-ratio: 4/3;
  transition: transform 0.2s ease;
}

.image-card:hover {
  transform: scale(1.02);
}

.image-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 1rem;
}

.image-actions {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
}

/* Theme switching shimmer effect */
.theme-switching-shimmer {
  position: relative;
  overflow: hidden;
}

.theme-switching-shimmer::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
  animation: shimmer 0.8s ease-in-out;
  z-index: 1;
  pointer-events: none;
}

/* Theme switching ripple effect */
.theme-switching-ripple {
  position: relative;
  overflow: hidden;
}

.theme-switching-ripple::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: rippleElement 0.8s ease-out;
  z-index: 1;
  pointer-events: none;
}

/* Theme switching fade effect */
.theme-switching-fade {
  position: relative;
  overflow: hidden;
}

.theme-switching-fade::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(59, 130, 246, 0.2);
  animation: fadeElement 0.8s ease-in-out;
  z-index: 1;
  pointer-events: none;
}

/* Theme switching slide effect */
.theme-switching-slide {
  position: relative;
  overflow: hidden;
}

.theme-switching-slide::before {
  content: "";
  position: absolute;
  top: -100%;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, rgba(59, 130, 246, 0.3), rgba(147, 51, 234, 0.3));
  animation: slideElement 0.8s ease-in-out;
  z-index: 1;
  pointer-events: none;
}

/* 基础动画关键帧 */
@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

@keyframes rippleElement {
  0% { 
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% { 
    width: 300%;
    height: 300%;
    opacity: 0;
  }
}

@keyframes fadeElement {
  0% { opacity: 0; }
  50% { opacity: 1; }
  100% { opacity: 0; }
}

@keyframes slideElement {
  0% { top: -100%; }
  30% { top: 0; }
  70% { top: 0; }
  100% { top: 100%; }
}

/* 动画预览效果 */
@keyframes shimmerPreview {
  0% { left: -100%; }
  100% { left: 100%; }
}

@keyframes ripplePreview {
  0% { 
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% { 
    width: 200vw;
    height: 200vw;
    opacity: 0;
  }
}

@keyframes fadePreview {
  0% { opacity: 0; }
  50% { opacity: 1; }
  100% { opacity: 0; }
}

@keyframes slidePreview {
  0% { top: -100%; }
  50% { top: 0; }
  100% { top: 100%; }
}

/* 主题切换动画效果 */
@keyframes shimmerTransition {
  0% { left: -100%; }
  100% { left: 100%; }
}

@keyframes rippleTransition {
  0% { 
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% { 
    width: 300vw;
    height: 300vw;
    opacity: 0;
  }
}

@keyframes fadeTransition {
  0% { opacity: 0; }
  50% { opacity: 1; }
  100% { opacity: 0; }
}

@keyframes slideTransition {
  0% { top: -100%; }
  30% { top: 0; }
  70% { top: 0; }
  100% { top: 100%; }
}

/* 侧边栏收缩状态优化 */
[data-sidebar="sidebar"][data-state="collapsed"] {
  width: 3rem !important;
}

[data-sidebar="sidebar"][data-state="collapsed"] .sidebar-menu-button {
  width: 2.5rem;
  height: 2.5rem;
  padding: 0;
  justify-content: center;
  margin: 0 auto;
}

[data-sidebar="sidebar"][data-state="collapsed"] .sidebar-menu-button > div:first-child {
  margin: 0;
}

[data-sidebar="sidebar"][data-state="collapsed"] .sidebar-group-label {
  display: none;
}

[data-sidebar="sidebar"][data-state="collapsed"] .separator {
  width: 1.5rem;
  margin: 0.5rem auto;
}

/* 确保图标容器不变形 */
.sidebar-menu-button > div:first-child {
  flex-shrink: 0;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 收缩状态下的工具提示样式 */
[data-sidebar="sidebar"][data-state="collapsed"] [data-tooltip] {
  position: relative;
}

/* 平滑过渡 */
[data-sidebar="sidebar"] {
  transition: width 0.2s ease-in-out;
}

.sidebar-menu-button {
  transition: all 0.2s ease-in-out;
}
