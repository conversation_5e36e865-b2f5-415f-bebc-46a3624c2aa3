@echo off
chcp 65001 >nul

:: =============================================================================
:: Civitai Explorer 快速启动脚本 (简化版)
:: =============================================================================

title Civitai Explorer - 快速启动

echo.
echo 🚀 Civitai Explorer 快速启动中...
echo.

:: 检查项目文件
if not exist "package.json" (
    echo ❌ 错误: 请在项目根目录下运行此脚本
    pause
    exit /b 1
)

:: 检查并安装依赖
if not exist "node_modules" (
    echo 📦 首次运行，正在安装依赖...
    
    :: 优先使用 pnpm
    pnpm --version >nul 2>&1
    if errorlevel 1 (
        echo 📦 使用 npm 安装依赖...
        npm install
    ) else (
        echo 📦 使用 pnpm 安装依赖...
        pnpm install
    )
    
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
)

:: 启动开发服务器
echo.
echo 🌟 启动开发服务器...
echo 📍 本地访问: http://localhost:3000
echo 💡 按 Ctrl+C 停止服务器
echo.

:: 延迟2秒后打开浏览器
timeout /t 2 /nobreak >nul
start http://localhost:3000

:: 检查包管理器并启动
pnpm --version >nul 2>&1
if errorlevel 1 (
    npm run dev
) else (
    pnpm dev
)

echo.
echo 👋 服务器已停止
pause
