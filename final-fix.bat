@echo off

:: =============================================================================
:: Civitai Explorer FINAL Theme Fix Script
:: =============================================================================

title Civitai Explorer - FINAL FIX

echo.
echo Civitai Explorer FINAL Theme Fix
echo =================================
echo.

echo 🎉 PROBLEM IDENTIFIED AND FIXED!
echo.
echo ISSUE FOUND:
echo -----------
echo CSS variables contained HSL values like "221.2 83.2%% 53.3%%"
echo But CSS was using: hsl(var(--theme-primary))
echo This created invalid syntax: hsl(221.2 83.2%% 53.3%%)
echo.
echo SOLUTION APPLIED:
echo ----------------
echo Changed CSS variables to complete HSL values:
echo    --theme-primary: hsl(221.2, 83.2%%, 53.3%%)
echo.
echo Changed CSS usage to direct variable reference:
echo    background-color: var(--theme-primary)
echo.
echo This creates valid CSS:
echo    background-color: hsl(221.2, 83.2%%, 53.3%%)
echo.

:: Clear Next.js cache
echo [STEP 1] Clearing Next.js cache...
if exist ".next" (
    echo Removing .next directory...
    rmdir /s /q ".next"
    echo Done.
) else (
    echo .next directory not found, skipping...
)

echo.
echo [SUCCESS] FINAL FIX APPLIED!
echo.
echo WHAT SHOULD WORK NOW:
echo --------------------
echo All theme buttons should display correct colors
echo Theme cards should have colored borders
echo Theme badges should use theme colors
echo Theme links should use theme colors
echo Theme switching should work for all components
echo Both simplified and original theme tests should work
echo.
echo VERIFICATION CHECKLIST:
echo ----------------------
echo [ ] Simplified theme test works (should already work)
echo [ ] Original theme test now works (newly fixed)
echo [ ] CSS class test button shows theme color
echo [ ] HSL wrapper syntax test works
echo [ ] Direct variable reference test works
echo [ ] Theme switching updates all components
echo.

pause

echo.
echo Please restart your development server now:
echo 1. Stop current server (Ctrl+C in terminal)
echo 2. Run: quick.bat or start.bat
echo 3. Go to Dashboard theme test sections
echo 4. Test BOTH theme areas - they should both work now!
echo.
echo This should be the final fix!
echo All theme components should now properly apply colors.
echo.

pause
