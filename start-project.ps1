# =============================================================================
# Civitai Explorer PowerShell 启动脚本
# =============================================================================

# 设置控制台编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# 设置窗口标题
$Host.UI.RawUI.WindowTitle = "Civitai Explorer - PowerShell 启动器"

# 显示欢迎信息
Write-Host ""
Write-Host "╔══════════════════════════════════════════════════════════════════════════════╗" -ForegroundColor Green
Write-Host "║                          🚀 Civitai Explorer 启动器                          ║" -ForegroundColor Green
Write-Host "║                                                                              ║" -ForegroundColor Green
Write-Host "║                     现代化的图像与元数据管理应用                               ║" -ForegroundColor Green
Write-Host "╚══════════════════════════════════════════════════════════════════════════════╝" -ForegroundColor Green
Write-Host ""

# 检查当前目录
if (-not (Test-Path "package.json")) {
    Write-Host "❌ 错误: 未找到 package.json 文件" -ForegroundColor Red
    Write-Host "   请确保在项目根目录下运行此脚本" -ForegroundColor Yellow
    Read-Host "按回车键退出"
    exit 1
}

# 检查 Node.js
Write-Host "🔍 检查 Node.js 环境..." -ForegroundColor Cyan
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js 版本: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 错误: 未找到 Node.js" -ForegroundColor Red
    Write-Host "   请先安装 Node.js: https://nodejs.org/" -ForegroundColor Yellow
    Read-Host "按回车键退出"
    exit 1
}

# 检查包管理器
Write-Host "🔍 检查包管理器..." -ForegroundColor Cyan
$packageManager = "npm"
$devCommand = "npm run dev"

try {
    $pnpmVersion = pnpm --version
    Write-Host "✅ pnpm 版本: $pnpmVersion" -ForegroundColor Green
    $packageManager = "pnpm"
    $devCommand = "pnpm dev"
} catch {
    Write-Host "⚠️  未找到 pnpm，将使用 npm" -ForegroundColor Yellow
}

# 主菜单函数
function Show-Menu {
    Write-Host ""
    Write-Host "╔══════════════════════════════════════════════════════════════════════════════╗" -ForegroundColor Blue
    Write-Host "║                                🎯 操作菜单                                   ║" -ForegroundColor Blue
    Write-Host "╠══════════════════════════════════════════════════════════════════════════════╣" -ForegroundColor Blue
    Write-Host "║  [1] 🚀 快速启动开发服务器 (推荐)                                             ║" -ForegroundColor White
    Write-Host "║  [2] 📦 安装/更新依赖                                                        ║" -ForegroundColor White
    Write-Host "║  [3] 🏗️  构建生产版本                                                         ║" -ForegroundColor White
    Write-Host "║  [4] 🌐 启动生产服务器                                                        ║" -ForegroundColor White
    Write-Host "║  [5] 🔧 运行代码检查 (ESLint)                                                 ║" -ForegroundColor White
    Write-Host "║  [6] 🧹 清理缓存和重新安装                                                     ║" -ForegroundColor White
    Write-Host "║  [7] 📊 显示项目信息                                                          ║" -ForegroundColor White
    Write-Host "║  [8] 🌍 在浏览器中打开应用                                                     ║" -ForegroundColor White
    Write-Host "║  [0] ❌ 退出                                                                  ║" -ForegroundColor White
    Write-Host "╚══════════════════════════════════════════════════════════════════════════════╝" -ForegroundColor Blue
    Write-Host ""
}

# 启动开发服务器
function Start-DevServer {
    Write-Host ""
    Write-Host "🚀 启动开发服务器..." -ForegroundColor Green
    Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Gray
    Write-Host ""

    # 检查依赖
    if (-not (Test-Path "node_modules")) {
        Write-Host "📦 首次运行，正在安装依赖..." -ForegroundColor Yellow
        & $packageManager install
        if ($LASTEXITCODE -ne 0) {
            Write-Host "❌ 依赖安装失败" -ForegroundColor Red
            Read-Host "按回车键继续"
            return
        }
        Write-Host "✅ 依赖安装完成" -ForegroundColor Green
    }

    Write-Host "🌟 正在启动 Civitai Explorer..." -ForegroundColor Magenta
    Write-Host "📍 本地地址: http://localhost:3000" -ForegroundColor Cyan
    Write-Host "🔗 网络地址: http://0.0.0.0:3000" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "💡 提示:" -ForegroundColor Yellow
    Write-Host "   - 按 Ctrl+C 停止服务器" -ForegroundColor Gray
    Write-Host "   - 修改代码后会自动重新加载" -ForegroundColor Gray
    Write-Host "   - 首次启动可能需要几秒钟时间" -ForegroundColor Gray
    Write-Host ""

    # 延迟后打开浏览器
    Start-Sleep -Seconds 3
    Start-Process "http://localhost:3000"

    # 启动开发服务器
    if ($packageManager -eq "pnpm") {
        & pnpm dev
    } else {
        & npm run dev
    }
}

# 安装依赖
function Install-Dependencies {
    Write-Host ""
    Write-Host "📦 安装/更新项目依赖..." -ForegroundColor Green
    Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Gray
    
    & $packageManager install
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 依赖安装成功" -ForegroundColor Green
    } else {
        Write-Host "❌ 依赖安装失败" -ForegroundColor Red
    }
    Read-Host "按回车键继续"
}

# 构建生产版本
function Build-Production {
    Write-Host ""
    Write-Host "🏗️ 构建生产版本..." -ForegroundColor Green
    Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Gray
    
    & $packageManager run build
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 构建成功" -ForegroundColor Green
        Write-Host "📁 构建文件位于 .next 目录" -ForegroundColor Cyan
    } else {
        Write-Host "❌ 构建失败" -ForegroundColor Red
    }
    Read-Host "按回车键继续"
}

# 显示项目信息
function Show-ProjectInfo {
    Write-Host ""
    Write-Host "📊 项目信息" -ForegroundColor Green
    Write-Host "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━" -ForegroundColor Gray
    Write-Host "📁 项目名称: Civitai Explorer" -ForegroundColor White
    Write-Host "🏷️  版本: 0.1.0" -ForegroundColor White
    Write-Host "🛠️  框架: Next.js 15.2.4 + React 19" -ForegroundColor White
    Write-Host "📦 包管理器: $packageManager" -ForegroundColor White
    Write-Host "🎨 UI 库: Radix UI + Tailwind CSS" -ForegroundColor White
    Write-Host "🌐 开发端口: 3000" -ForegroundColor White
    Write-Host "📍 项目路径: $(Get-Location)" -ForegroundColor White
    Write-Host ""
    Write-Host "🔗 相关链接:" -ForegroundColor Yellow
    Write-Host "   • Next.js: https://nextjs.org/" -ForegroundColor Gray
    Write-Host "   • React: https://react.dev/" -ForegroundColor Gray
    Write-Host "   • Tailwind CSS: https://tailwindcss.com/" -ForegroundColor Gray
    Write-Host "   • Civitai API: https://github.com/civitai/civitai/wiki/REST-API-Reference" -ForegroundColor Gray
    Write-Host ""
    Read-Host "按回车键继续"
}

# 主循环
do {
    Show-Menu
    $choice = Read-Host "请选择操作 (0-8)"
    
    switch ($choice) {
        "1" { Start-DevServer }
        "2" { Install-Dependencies }
        "3" { Build-Production }
        "4" { 
            Write-Host "🌐 启动生产服务器..." -ForegroundColor Green
            if (-not (Test-Path ".next")) {
                Write-Host "⚠️  未找到构建文件，正在构建..." -ForegroundColor Yellow
                & $packageManager run build
            }
            Start-Process "http://localhost:3000"
            & $packageManager run start
        }
        "5" { 
            Write-Host "🔧 运行 ESLint 代码检查..." -ForegroundColor Green
            & $packageManager run lint
            Read-Host "按回车键继续"
        }
        "6" { 
            Write-Host "🧹 清理缓存和重新安装..." -ForegroundColor Green
            if (Test-Path "node_modules") { Remove-Item -Recurse -Force "node_modules" }
            if (Test-Path ".next") { Remove-Item -Recurse -Force ".next" }
            & $packageManager install
            Read-Host "按回车键继续"
        }
        "7" { Show-ProjectInfo }
        "8" { 
            Write-Host "🌍 在浏览器中打开应用..." -ForegroundColor Green
            Start-Process "http://localhost:3000"
            Read-Host "按回车键继续"
        }
        "0" { 
            Write-Host ""
            Write-Host "👋 感谢使用 Civitai Explorer 启动器！" -ForegroundColor Green
            Write-Host "大叔，我们一起加油！" -ForegroundColor Magenta
            Write-Host ""
            exit 0
        }
        default { 
            Write-Host "❌ 无效选择，请重新输入" -ForegroundColor Red
        }
    }
} while ($true)
