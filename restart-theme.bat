@echo off

:: =============================================================================
:: Civitai Explorer Theme Enhancement Restart Script
:: =============================================================================

title Civitai Explorer - Theme Enhancement

echo.
echo Civitai Explorer Theme Enhancement
echo ==================================
echo.

echo This script will restart the development server to apply theme enhancements:
echo.
echo [ENHANCEMENTS APPLIED]
echo + Enhanced theme color application to buttons, cards, badges
echo + Increased transparency for modals and menus
echo + Added theme test components for verification
echo + Fixed hydration and nested button issues
echo + Added new button variants: theme, theme-outline
echo + Enhanced card hover effects and theme integration
echo.

:: Clear Next.js cache
echo [STEP 1] Clearing Next.js cache...
if exist ".next" (
    echo Removing .next directory...
    rmdir /s /q ".next"
    echo Done.
) else (
    echo .next directory not found, skipping...
)

echo.
echo [SUCCESS] Theme enhancements ready!
echo.
echo WHAT'S NEW:
echo -----------
echo 1. ENHANCED THEME COLORS:
echo    - Buttons now use theme colors (try theme and theme-outline variants)
echo    - Cards have theme-colored borders and hover effects
echo    - Badges apply theme colors (theme-badge, theme-badge-solid classes)
echo    - Input fields have theme-colored focus states
echo    - Links use theme colors
echo.
echo 2. INCREASED TRANSPARENCY:
echo    - Modal/menu opacity reduced from 0.75 to 0.45 (light mode)
echo    - Modal/menu opacity reduced from 0.6 to 0.35 (dark mode)
echo    - Dropdown menus are more transparent
echo.
echo 3. NEW THEME TEST SECTION:
echo    - Added comprehensive theme test components to dashboard
echo    - Test all theme variations in one place
echo    - Visual verification of theme color application
echo.
echo HOW TO TEST:
echo -----------
echo 1. Start the development server (quick.bat or start.bat)
echo 2. Go to Dashboard page
echo 3. Scroll down to see "Theme Test Components" section
echo 4. Click the palette icon in sidebar to change themes
echo 5. Watch all components change colors in real-time!
echo.
echo AVAILABLE THEME CLASSES:
echo -----------------------
echo - theme-button          : Primary theme button
echo - theme-button-outline  : Outline theme button  
echo - theme-card           : Theme-colored card borders/hover
echo - theme-badge          : Theme-colored badge
echo - theme-badge-solid    : Solid theme badge
echo - theme-input          : Theme-colored input focus
echo - theme-link           : Theme-colored links
echo - theme-background     : Theme gradient background
echo - theme-background-solid : Solid theme gradient
echo.

pause

echo.
echo Please restart your development server now:
echo 1. Stop current server (Ctrl+C in terminal)
echo 2. Run: quick.bat or start.bat
echo 3. Go to Dashboard to see the new theme test section
echo.
echo Enjoy the enhanced theme system!
echo.

pause
