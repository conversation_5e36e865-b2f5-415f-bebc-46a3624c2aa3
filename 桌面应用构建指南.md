# 🖥️ Civitai Explorer 桌面应用构建指南

本指南将帮助您将 Civitai Explorer Web 应用封装成跨平台桌面应用程序。

## 📋 技术方案

### 选择的技术栈
- **Electron**: 主要封装技术
- **Next.js**: Web 应用框架
- **Electron Builder**: 打包和分发工具
- **Auto Updater**: 自动更新功能

### 优势
- ✅ 跨平台支持（Windows、macOS、Linux）
- ✅ 原生桌面体验
- ✅ 文件系统访问
- ✅ 系统通知
- ✅ 自动更新
- ✅ 离线使用

## 🚀 快速开始

### 方法 1：使用构建脚本（推荐）

#### 开发模式
```bash
# 双击运行
dev-desktop.bat
```

#### 构建生产版本
```bash
# 双击运行
build-desktop.bat
```

### 方法 2：手动命令

#### 安装依赖
```bash
npm install electron electron-builder electron-updater concurrently wait-on --save-dev
```

#### 开发模式
```bash
# 启动开发模式（同时运行 Next.js 和 Electron）
npm run electron-dev
```

#### 构建应用
```bash
# 构建 Windows 版本
npm run electron-build-win

# 构建 macOS 版本
npm run electron-build-mac

# 构建 Linux 版本
npm run electron-build-linux

# 构建所有平台
npm run electron-build
```

## 📁 项目结构

```
civitai-explorer/
├── electron/                 # Electron 主进程文件
│   ├── main.js              # 主进程入口
│   ├── preload.js           # 预加载脚本
│   └── installer.nsh        # Windows 安装程序脚本
├── out/                     # Next.js 静态导出文件
├── dist/                    # 构建输出目录
├── public/                  # 静态资源
│   ├── icon.ico            # Windows 图标
│   ├── icon.icns           # macOS 图标
│   └── icon.png            # Linux 图标
├── electron-builder.json   # 构建配置
├── next.config.mjs         # Next.js 配置
└── package.json            # 项目配置
```

## ⚙️ 配置说明

### package.json 配置
```json
{
  "main": "electron/main.js",
  "homepage": "./",
  "scripts": {
    "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron .\"",
    "electron-build": "npm run export && electron-builder"
  }
}
```

### Next.js 配置 (next.config.mjs)
```javascript
const nextConfig = {
  output: 'export',           // 静态导出
  trailingSlash: true,        // 添加尾部斜杠
  distDir: 'out',            // 输出目录
  images: {
    unoptimized: true         // 禁用图片优化
  }
}
```

### Electron Builder 配置 (electron-builder.json)
```json
{
  "appId": "com.civitai.explorer",
  "productName": "Civitai Explorer",
  "directories": {
    "output": "dist"
  },
  "win": {
    "target": ["nsis", "portable"],
    "icon": "public/icon.ico"
  }
}
```

## 🎯 核心功能

### 主进程功能 (electron/main.js)
- 🪟 窗口管理和配置
- 📋 应用菜单创建
- 🔒 安全设置
- 🔄 自动更新检查
- 💬 IPC 通信处理

### 预加载脚本 (electron/preload.js)
- 🔐 安全的 API 暴露
- 📁 文件对话框
- 📢 系统通知
- 🖥️ 平台信息

### 安全特性
- ✅ 禁用 Node.js 集成
- ✅ 启用上下文隔离
- ✅ 预加载脚本安全通信
- ✅ 外部链接安全处理

## 📦 构建输出

### Windows
- `Civitai Explorer Setup.exe` - 安装程序
- `Civitai Explorer Portable.exe` - 便携版

### macOS
- `Civitai Explorer.dmg` - 磁盘映像
- `Civitai Explorer.app` - 应用程序包

### Linux
- `Civitai Explorer.AppImage` - AppImage 格式
- `civitai-explorer.deb` - Debian 包

## 🔧 自定义配置

### 修改应用图标
1. 准备图标文件：
   - Windows: `public/icon.ico` (256x256)
   - macOS: `public/icon.icns` (512x512)
   - Linux: `public/icon.png` (512x512)

2. 更新 `electron-builder.json` 中的图标路径

### 修改应用信息
编辑 `electron-builder.json`：
```json
{
  "appId": "com.yourcompany.yourapp",
  "productName": "Your App Name",
  "description": "Your app description"
}
```

### 添加自动更新
1. 配置发布提供商（GitHub、S3 等）
2. 在 `electron-builder.json` 中设置 `publish` 配置
3. 主进程会自动检查和下载更新

## 🐛 故障排除

### 常见问题

#### 1. 构建失败
```bash
# 清理缓存
rm -rf node_modules out dist
npm install
npm run build
```

#### 2. Electron 窗口空白
- 检查 Next.js 是否正确导出
- 确认 `out` 目录存在且包含 `index.html`
- 检查控制台错误信息

#### 3. 图标不显示
- 确保图标文件存在且格式正确
- Windows 需要 `.ico` 格式
- macOS 需要 `.icns` 格式

#### 4. 应用无法启动
- 检查 `electron/main.js` 语法错误
- 确认所有依赖已安装
- 查看终端错误信息

### 调试技巧

#### 开发模式调试
```bash
# 启动开发模式并打开开发者工具
npm run electron-dev
```

#### 生产版本调试
在 `electron/main.js` 中临时启用开发者工具：
```javascript
mainWindow.webContents.openDevTools()
```

## 📈 性能优化

### 减小应用体积
1. 使用 `electron-builder` 的压缩选项
2. 排除不必要的文件
3. 优化图片和资源

### 提升启动速度
1. 延迟加载非关键模块
2. 使用 `ready-to-show` 事件
3. 预加载关键资源

## 🚀 分发部署

### 本地分发
1. 构建应用：`npm run electron-build`
2. 在 `dist` 目录找到安装包
3. 分发给用户

### 自动分发
1. 配置 GitHub Releases
2. 设置 CI/CD 流水线
3. 自动构建和发布

## 📚 进阶功能

### 添加原生模块
```bash
# 安装原生模块
npm install native-module
# 重新构建
npm run electron-rebuild
```

### 自定义协议
在 `electron/main.js` 中注册自定义协议：
```javascript
app.setAsDefaultProtocolClient('civitai-explorer')
```

### 系统托盘
添加系统托盘图标和菜单：
```javascript
const { Tray } = require('electron')
const tray = new Tray('path/to/icon.png')
```

## 🎉 完成

现在您已经成功将 Civitai Explorer 封装为桌面应用！

### 下一步
1. 测试所有功能
2. 准备应用图标
3. 配置自动更新
4. 分发给用户

---

**祝您构建成功！如有问题，请查看错误日志或寻求技术支持。**
