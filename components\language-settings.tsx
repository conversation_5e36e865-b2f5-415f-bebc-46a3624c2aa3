"use client"
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Languages } from "lucide-react"
import { useLanguage, type Language } from "@/contexts/language-context"

const languageOptions = [
  {
    id: "zh" as Language,
    name: "中文",
    description: "简体中文",
    flag: "🇨🇳",
  },
  {
    id: "en" as Language,
    name: "English",
    description: "English (US)",
    flag: "🇺🇸",
  },
]

export function LanguageSettings() {
  const { language, setLanguage, t } = useLanguage()

  const handleLanguageChange = (newLanguage: Language) => {
    setLanguage(newLanguage)
  }

  return (
    <Card className="frosted-card">
      <CardHeader>
        <CardTitle className="text-base flex items-center gap-2">
          <Languages className="h-4 w-4" />
          {t("settings.language")}
        </CardTitle>
        <CardDescription>{t("settings.languageDescription")}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <RadioGroup value={language} onValueChange={handleLanguageChange}>
          {languageOptions.map((option) => (
            <div
              key={option.id}
              className="flex items-center space-x-3 p-3 rounded-lg border border-white/20 dark:border-white/10 hover:bg-white/10 dark:hover:bg-white/5 transition-colors"
            >
              <RadioGroupItem value={option.id} id={option.id} />
              <div className="flex items-center gap-3 flex-1">
                <div className="text-2xl">{option.flag}</div>
                <div>
                  <Label htmlFor={option.id} className="font-medium cursor-pointer">
                    {option.name}
                  </Label>
                  <p className="text-xs text-slate-600 dark:text-slate-400">{option.description}</p>
                </div>
              </div>
              {language === option.id && <div className="h-2 w-2 rounded-full bg-blue-500 animate-pulse"></div>}
            </div>
          ))}
        </RadioGroup>

        <div className="mt-4 p-3 rounded-lg bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800">
          <p className="text-xs text-blue-700 dark:text-blue-300">
            {language === "zh"
              ? "🌐 语言设置会立即应用到整个应用程序界面"
              : "🌐 Language settings will be applied immediately to the entire application interface"}
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
