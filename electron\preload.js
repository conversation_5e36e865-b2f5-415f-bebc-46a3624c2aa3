const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron')

// 暴露安全的 API 给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 应用信息
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  
  // 文件对话框
  showSaveDialog: () => ipcRenderer.invoke('show-save-dialog'),
  showOpenDialog: () => ipcRenderer.invoke('show-open-dialog'),
  
  // 平台信息
  platform: process.platform,
  
  // 通知
  showNotification: (title, body) => {
    new Notification(title, { body })
  },
  
  // 检查是否在 Electron 环境中
  isElectron: true
})

// 防止页面被拖拽文件替换
document.addEventListener('DOMContentLoaded', () => {
  document.addEventListener('dragover', (e) => {
    e.preventDefault()
    e.stopPropagation()
  })
  
  document.addEventListener('drop', (e) => {
    e.preventDefault()
    e.stopPropagation()
  })
})
