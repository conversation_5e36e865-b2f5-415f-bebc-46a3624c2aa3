'use client'

import React, { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Separator } from '@/components/ui/separator'

export function ThemeTest() {
  const [themeVars, setThemeVars] = useState<Record<string, string>>({})

  useEffect(() => {
    // 获取当前主题变量值用于调试
    const updateThemeVars = () => {
      const root = document.documentElement
      const computedStyle = getComputedStyle(root)

      const vars = {
        '--theme-primary': computedStyle.getPropertyValue('--theme-primary').trim(),
        '--theme-primary-foreground': computedStyle.getPropertyValue('--theme-primary-foreground').trim(),
        '--theme-secondary': computedStyle.getPropertyValue('--theme-secondary').trim(),
        '--theme-gradient-from': computedStyle.getPropertyValue('--theme-gradient-from').trim(),
        '--theme-gradient-to': computedStyle.getPropertyValue('--theme-gradient-to').trim(),
      }

      setThemeVars(vars)
    }

    // 初始更新
    updateThemeVars()

    // 监听主题变化
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' &&
            (mutation.attributeName === 'class' || mutation.attributeName === 'style')) {
          updateThemeVars()
        }
      })
    })

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class', 'style']
    })

    // 定期更新（备用方案）
    const interval = setInterval(updateThemeVars, 1000)

    return () => {
      observer.disconnect()
      clearInterval(interval)
    }
  }, [])

  return (
    <div className="p-6 space-y-6">
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">主题测试组件</h2>
        <p className="text-muted-foreground">测试各种组件的主题色彩应用效果</p>

        {/* 调试信息 */}
        <Card className="bg-gray-50 dark:bg-gray-900 border-dashed">
          <CardHeader>
            <CardTitle className="text-sm">🔍 主题变量调试信息</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs font-mono">
              {Object.entries(themeVars).map(([key, value]) => (
                <div key={key} className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">{key}:</span>
                  <span className="text-gray-900 dark:text-gray-100">{value || '未设置'}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 按钮测试 */}
      <Card className="theme-card">
        <CardHeader>
          <CardTitle>按钮样式测试</CardTitle>
          <CardDescription>测试不同变体的按钮主题应用</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-3">
            <Button variant="default">默认按钮</Button>
            <Button variant="theme">主题按钮</Button>
            <Button variant="theme-outline">主题轮廓按钮</Button>
            <Button variant="secondary">次要按钮</Button>
            <Button variant="outline">轮廓按钮</Button>
            <Button variant="ghost">幽灵按钮</Button>
          </div>

          {/* 内联样式测试 */}
          <div className="mt-4 space-y-2">
            <p className="text-sm font-medium">内联样式测试（确保变量可读取）：</p>
            <div className="flex flex-wrap gap-3">
              <button
                className="px-4 py-2 rounded-md transition-all"
                style={{
                  backgroundColor: `hsl(${themeVars['--theme-primary'] || '221.2 83.2% 53.3%'})`,
                  color: `hsl(${themeVars['--theme-primary-foreground'] || '210 40% 98%'})`,
                  border: `1px solid hsl(${themeVars['--theme-primary'] || '221.2 83.2% 53.3%'})`
                }}
              >
                内联主题按钮
              </button>
              <button
                className="px-4 py-2 rounded-md transition-all"
                style={{
                  backgroundColor: 'transparent',
                  color: `hsl(${themeVars['--theme-primary'] || '221.2 83.2% 53.3%'})`,
                  border: `2px solid hsl(${themeVars['--theme-primary'] || '221.2 83.2% 53.3%'})`
                }}
              >
                内联轮廓按钮
              </button>

              {/* CSS 变量直接测试 - 多种语法 */}
              <div
                className="px-4 py-2 rounded-md transition-all cursor-pointer"
                style={{
                  backgroundColor: `hsl(var(--theme-primary))`,
                  color: `hsl(var(--theme-primary-foreground))`,
                  border: `1px solid hsl(var(--theme-primary))`
                } as React.CSSProperties}
              >
                HSL包装语法
              </div>

              <div
                className="px-4 py-2 rounded-md transition-all cursor-pointer"
                style={{
                  backgroundColor: `var(--theme-primary)`,
                  color: `var(--theme-primary-foreground)`,
                  border: `1px solid var(--theme-primary)`
                } as React.CSSProperties}
              >
                直接变量引用
              </div>

              {/* 使用 CSS 类测试 */}
              <div className="px-4 py-2 rounded-md theme-button cursor-pointer">
                CSS类测试按钮
              </div>

              {/* 硬编码测试 */}
              <div
                className="px-4 py-2 rounded-md transition-all cursor-pointer"
                style={{
                  backgroundColor: `hsl(221.2, 83.2%, 53.3%)`,
                  color: `hsl(210, 40%, 98%)`,
                  border: `1px solid hsl(221.2, 83.2%, 53.3%)`
                } as React.CSSProperties}
              >
                硬编码蓝色测试
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 卡片测试 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card className="theme-card">
          <CardHeader>
            <CardTitle className="theme-icon">主题卡片 1</CardTitle>
            <CardDescription>这是一个应用了主题样式的卡片</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm">卡片内容区域，应该有主题色彩的边框和背景效果。</p>
          </CardContent>
        </Card>

        <Card className="theme-card">
          <CardHeader>
            <CardTitle className="theme-icon">主题卡片 2</CardTitle>
            <CardDescription>另一个主题卡片示例</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Badge className="theme-badge">主题徽章</Badge>
              <Badge className="theme-badge-solid">实心主题徽章</Badge>
            </div>
          </CardContent>
        </Card>

        <Card className="theme-background">
          <CardHeader>
            <CardTitle className="text-white">渐变背景卡片</CardTitle>
            <CardDescription className="text-white/80">使用主题渐变背景</CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="outline" className="border-white text-white hover:bg-white hover:text-black">
              白色按钮
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* 输入框测试 */}
      <Card className="theme-card">
        <CardHeader>
          <CardTitle>输入框样式测试</CardTitle>
          <CardDescription>测试表单元素的主题应用</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input placeholder="普通输入框" />
            <Input placeholder="主题输入框" className="theme-input" />
          </div>
        </CardContent>
      </Card>

      {/* 分隔线和链接测试 */}
      <Card className="theme-card">
        <CardHeader>
          <CardTitle>其他元素测试</CardTitle>
          <CardDescription>分隔线、链接等元素的主题应用</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <p>这是一个 <a href="#" className="theme-link">主题链接</a> 的示例。</p>
            <Separator className="theme-divider" />
            <p>分隔线上方和下方的内容。</p>
          </div>
          
          <div className="flex flex-wrap gap-2">
            <Badge>默认徽章</Badge>
            <Badge className="theme-badge">主题徽章</Badge>
            <Badge className="theme-badge-solid">实心主题徽章</Badge>
            <Badge variant="secondary">次要徽章</Badge>
            <Badge variant="outline">轮廓徽章</Badge>
          </div>
        </CardContent>
      </Card>

      {/* 渐变背景测试 */}
      <div className="theme-background-solid rounded-lg p-6">
        <h3 className="text-xl font-bold text-white mb-4">主题渐变背景区域</h3>
        <p className="text-white/90 mb-4">
          这个区域使用了主题的渐变背景色，文字应该是白色以确保可读性。
        </p>
        <div className="flex gap-3">
          <Button variant="outline" className="border-white text-white hover:bg-white hover:text-black">
            轮廓按钮
          </Button>
          <Button className="bg-white text-black hover:bg-white/90">
            白色按钮
          </Button>
        </div>
      </div>

      {/* 说明文字 */}
      <Card className="theme-card">
        <CardContent className="pt-6">
          <div className="text-center space-y-2">
            <p className="text-sm text-muted-foreground">
              🎨 切换不同的主题色彩，观察上述组件的颜色变化
            </p>
            <p className="text-xs text-muted-foreground">
              主题色彩应该应用到按钮、卡片边框、徽章、链接、输入框焦点等元素
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
