'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Separator } from '@/components/ui/separator'

export function ThemeTest() {
  return (
    <div className="p-6 space-y-6">
      <div className="space-y-4">
        <h2 className="text-2xl font-bold">主题测试组件</h2>
        <p className="text-muted-foreground">测试各种组件的主题色彩应用效果</p>
      </div>

      {/* 按钮测试 */}
      <Card className="theme-card">
        <CardHeader>
          <CardTitle>按钮样式测试</CardTitle>
          <CardDescription>测试不同变体的按钮主题应用</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-3">
            <Button variant="default">默认按钮</Button>
            <Button variant="theme">主题按钮</Button>
            <Button variant="theme-outline">主题轮廓按钮</Button>
            <Button variant="secondary">次要按钮</Button>
            <Button variant="outline">轮廓按钮</Button>
            <Button variant="ghost">幽灵按钮</Button>
          </div>
        </CardContent>
      </Card>

      {/* 卡片测试 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card className="theme-card">
          <CardHeader>
            <CardTitle className="theme-icon">主题卡片 1</CardTitle>
            <CardDescription>这是一个应用了主题样式的卡片</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm">卡片内容区域，应该有主题色彩的边框和背景效果。</p>
          </CardContent>
        </Card>

        <Card className="theme-card">
          <CardHeader>
            <CardTitle className="theme-icon">主题卡片 2</CardTitle>
            <CardDescription>另一个主题卡片示例</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Badge className="theme-badge">主题徽章</Badge>
              <Badge className="theme-badge-solid">实心主题徽章</Badge>
            </div>
          </CardContent>
        </Card>

        <Card className="theme-background">
          <CardHeader>
            <CardTitle className="text-white">渐变背景卡片</CardTitle>
            <CardDescription className="text-white/80">使用主题渐变背景</CardDescription>
          </CardHeader>
          <CardContent>
            <Button variant="outline" className="border-white text-white hover:bg-white hover:text-black">
              白色按钮
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* 输入框测试 */}
      <Card className="theme-card">
        <CardHeader>
          <CardTitle>输入框样式测试</CardTitle>
          <CardDescription>测试表单元素的主题应用</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input placeholder="普通输入框" />
            <Input placeholder="主题输入框" className="theme-input" />
          </div>
        </CardContent>
      </Card>

      {/* 分隔线和链接测试 */}
      <Card className="theme-card">
        <CardHeader>
          <CardTitle>其他元素测试</CardTitle>
          <CardDescription>分隔线、链接等元素的主题应用</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <p>这是一个 <a href="#" className="theme-link">主题链接</a> 的示例。</p>
            <Separator className="theme-divider" />
            <p>分隔线上方和下方的内容。</p>
          </div>
          
          <div className="flex flex-wrap gap-2">
            <Badge>默认徽章</Badge>
            <Badge className="theme-badge">主题徽章</Badge>
            <Badge className="theme-badge-solid">实心主题徽章</Badge>
            <Badge variant="secondary">次要徽章</Badge>
            <Badge variant="outline">轮廓徽章</Badge>
          </div>
        </CardContent>
      </Card>

      {/* 渐变背景测试 */}
      <div className="theme-background-solid rounded-lg p-6">
        <h3 className="text-xl font-bold text-white mb-4">主题渐变背景区域</h3>
        <p className="text-white/90 mb-4">
          这个区域使用了主题的渐变背景色，文字应该是白色以确保可读性。
        </p>
        <div className="flex gap-3">
          <Button variant="outline" className="border-white text-white hover:bg-white hover:text-black">
            轮廓按钮
          </Button>
          <Button className="bg-white text-black hover:bg-white/90">
            白色按钮
          </Button>
        </div>
      </div>

      {/* 说明文字 */}
      <Card className="theme-card">
        <CardContent className="pt-6">
          <div className="text-center space-y-2">
            <p className="text-sm text-muted-foreground">
              🎨 切换不同的主题色彩，观察上述组件的颜色变化
            </p>
            <p className="text-xs text-muted-foreground">
              主题色彩应该应用到按钮、卡片边框、徽章、链接、输入框焦点等元素
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
