"use client"

import { SidebarInset, SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { useNavigation } from "@/components/navigation-provider"
import { DashboardPage } from "@/components/pages/dashboard-page"
import { GalleryPage } from "@/components/pages/gallery-page"
import { DownloaderPage } from "@/components/pages/downloader-page"
import { MetadataPage } from "@/components/pages/metadata-page"
import { SettingsPage } from "@/components/pages/settings-page"
import { useLanguage } from "@/contexts/language-context"

const pageComponents = {
  dashboard: DashboardPage,
  gallery: GalleryPage,
  downloader: DownloaderPage,
  metadata: MetadataPage,
  settings: SettingsPage,
}

export function MainContent() {
  const { t } = useLanguage()
  const { currentPage } = useNavigation()
  const PageComponent = pageComponents[currentPage]

  const pageTitles = {
    dashboard: t("nav.dashboard"),
    gallery: t("nav.gallery"),
    downloader: t("nav.downloader"),
    metadata: t("nav.metadata"),
    settings: t("nav.settings"),
  }

  return (
    <SidebarInset>
      <header className="flex h-16 shrink-0 items-center gap-2 px-4 border-b border-white/20 dark:border-white/10 bg-white/20 dark:bg-black/20 backdrop-blur-xl">
        <SidebarTrigger className="text-slate-700 dark:text-slate-300 hover:bg-white/20 dark:hover:bg-white/10 -ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4 bg-white/20 dark:bg-white/10" />
        <h1 className="text-lg font-semibold text-slate-900 dark:text-slate-100">{pageTitles[currentPage]}</h1>
      </header>

      <div className="flex flex-1 flex-col gap-4 p-4 overflow-auto">
        <PageComponent />
      </div>
    </SidebarInset>
  )
}
