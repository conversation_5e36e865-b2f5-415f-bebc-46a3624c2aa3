@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: =============================================================================
:: Civitai Explorer 项目快速启动脚本
:: =============================================================================

title Civitai Explorer - 项目启动器

:: 设置颜色
color 0A

:: 显示欢迎信息
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                          🚀 Civitai Explorer 启动器                          ║
echo ║                                                                              ║
echo ║                     现代化的图像与元数据管理应用                               ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

:: 检查当前目录
if not exist "package.json" (
    echo ❌ 错误: 未找到 package.json 文件
    echo    请确保在项目根目录下运行此脚本
    echo.
    pause
    exit /b 1
)

:: 检查 Node.js
echo 🔍 检查 Node.js 环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到 Node.js
    echo    请先安装 Node.js: https://nodejs.org/
    echo.
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo ✅ Node.js 版本: !NODE_VERSION!
)

:: 检查 pnpm
echo 🔍 检查 pnpm 包管理器...
pnpm --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  未找到 pnpm，正在安装...
    npm install -g pnpm
    if errorlevel 1 (
        echo ❌ pnpm 安装失败，将使用 npm 作为替代
        set PACKAGE_MANAGER=npm
        set DEV_COMMAND=npm run dev
    ) else (
        echo ✅ pnpm 安装成功
        set PACKAGE_MANAGER=pnpm
        set DEV_COMMAND=pnpm dev
    )
) else (
    for /f "tokens=*" %%i in ('pnpm --version') do set PNPM_VERSION=%%i
    echo ✅ pnpm 版本: !PNPM_VERSION!
    set PACKAGE_MANAGER=pnpm
    set DEV_COMMAND=pnpm dev
)

:: 显示菜单
:menu
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                🎯 操作菜单                                   ║
echo ╠══════════════════════════════════════════════════════════════════════════════╣
echo ║  [1] 🚀 快速启动开发服务器 (推荐)                                             ║
echo ║  [2] 📦 安装/更新依赖                                                        ║
echo ║  [3] 🏗️  构建生产版本                                                         ║
echo ║  [4] 🌐 启动生产服务器                                                        ║
echo ║  [5] 🔧 运行代码检查 (ESLint)                                                 ║
echo ║  [6] 🧹 清理缓存和重新安装                                                     ║
echo ║  [7] 📊 显示项目信息                                                          ║
echo ║  [8] 🌍 在浏览器中打开应用                                                     ║
echo ║  [0] ❌ 退出                                                                  ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

set /p choice="请选择操作 (0-8): "

if "%choice%"=="1" goto start_dev
if "%choice%"=="2" goto install_deps
if "%choice%"=="3" goto build_prod
if "%choice%"=="4" goto start_prod
if "%choice%"=="5" goto run_lint
if "%choice%"=="6" goto clean_install
if "%choice%"=="7" goto show_info
if "%choice%"=="8" goto open_browser
if "%choice%"=="0" goto exit
echo ❌ 无效选择，请重新输入
goto menu

:: 快速启动开发服务器
:start_dev
echo.
echo 🚀 启动开发服务器...
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo.

:: 检查依赖是否已安装
if not exist "node_modules" (
    echo 📦 首次运行，正在安装依赖...
    %PACKAGE_MANAGER% install
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        goto menu
    )
    echo ✅ 依赖安装完成
    echo.
)

echo 🌟 正在启动 Civitai Explorer...
echo 📍 本地地址: http://localhost:3000
echo 🔗 网络地址: http://0.0.0.0:3000
echo.
echo 💡 提示: 
echo    - 按 Ctrl+C 停止服务器
echo    - 修改代码后会自动重新加载
echo    - 首次启动可能需要几秒钟时间
echo.

:: 延迟3秒后自动打开浏览器
timeout /t 3 /nobreak >nul
start http://localhost:3000

%DEV_COMMAND%
goto menu

:: 安装依赖
:install_deps
echo.
echo 📦 安装/更新项目依赖...
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
%PACKAGE_MANAGER% install
if errorlevel 1 (
    echo ❌ 依赖安装失败
) else (
    echo ✅ 依赖安装成功
)
echo.
pause
goto menu

:: 构建生产版本
:build_prod
echo.
echo 🏗️ 构建生产版本...
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
%PACKAGE_MANAGER% run build
if errorlevel 1 (
    echo ❌ 构建失败
) else (
    echo ✅ 构建成功
    echo 📁 构建文件位于 .next 目录
)
echo.
pause
goto menu

:: 启动生产服务器
:start_prod
echo.
echo 🌐 启动生产服务器...
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

:: 检查是否已构建
if not exist ".next" (
    echo ⚠️  未找到构建文件，正在构建...
    %PACKAGE_MANAGER% run build
    if errorlevel 1 (
        echo ❌ 构建失败，无法启动生产服务器
        pause
        goto menu
    )
)

echo 🚀 生产服务器启动中...
echo 📍 访问地址: http://localhost:3000
echo.
start http://localhost:3000
%PACKAGE_MANAGER% run start
goto menu

:: 运行代码检查
:run_lint
echo.
echo 🔧 运行 ESLint 代码检查...
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
%PACKAGE_MANAGER% run lint
echo.
pause
goto menu

:: 清理并重新安装
:clean_install
echo.
echo 🧹 清理缓存和重新安装...
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo 🗑️  删除 node_modules...
if exist "node_modules" rmdir /s /q "node_modules"

echo 🗑️  删除 package-lock.json...
if exist "package-lock.json" del "package-lock.json"

echo 🗑️  删除 pnpm-lock.yaml...
if exist "pnpm-lock.yaml" del "pnpm-lock.yaml"

echo 🗑️  清理 Next.js 缓存...
if exist ".next" rmdir /s /q ".next"

echo 📦 重新安装依赖...
%PACKAGE_MANAGER% install

if errorlevel 1 (
    echo ❌ 重新安装失败
) else (
    echo ✅ 清理和重新安装完成
)
echo.
pause
goto menu

:: 显示项目信息
:show_info
echo.
echo 📊 项目信息
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo 📁 项目名称: Civitai Explorer
echo 🏷️  版本: 0.1.0
echo 🛠️  框架: Next.js 15.2.4 + React 19
echo 📦 包管理器: !PACKAGE_MANAGER!
echo 🎨 UI 库: Radix UI + Tailwind CSS
echo 🌐 开发端口: 3000
echo 📍 项目路径: %CD%
echo.

echo 📋 可用脚本:
echo   • dev    - 启动开发服务器
echo   • build  - 构建生产版本  
echo   • start  - 启动生产服务器
echo   • lint   - 运行代码检查
echo.

echo 🔗 相关链接:
echo   • Next.js: https://nextjs.org/
echo   • React: https://react.dev/
echo   • Tailwind CSS: https://tailwindcss.com/
echo   • Civitai API: https://github.com/civitai/civitai/wiki/REST-API-Reference
echo.
pause
goto menu

:: 在浏览器中打开
:open_browser
echo.
echo 🌍 在浏览器中打开应用...
start http://localhost:3000
echo ✅ 已尝试打开浏览器
echo 💡 如果应用未运行，请先选择选项 1 启动开发服务器
echo.
pause
goto menu

:: 退出
:exit
echo.
echo 👋 感谢使用 Civitai Explorer 启动器！
echo 🎯 项目地址: %CD%
echo 📚 如需帮助，请查看项目文档
echo.
echo 大叔，我们一起加油！
echo.
pause
exit /b 0
