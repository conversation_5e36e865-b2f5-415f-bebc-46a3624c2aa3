@echo off

:: =============================================================================
:: Civitai Explorer Desktop Test Script
:: =============================================================================

title Civitai Explorer - Desktop Test

echo.
echo Civitai Explorer Desktop Test
echo =============================
echo.

echo This script will test each component separately to identify issues.
echo.

:menu
echo.
echo TEST MENU:
echo [1] Test Next.js development server only
echo [2] Test Electron only (requires Next.js running)
echo [3] Install Electron dependencies
echo [4] Test full desktop development mode
echo [5] Check dependencies status
echo [0] Exit
echo.

set /p choice="Please select test (0-5): "

if "%choice%"=="1" goto test_nextjs
if "%choice%"=="2" goto test_electron
if "%choice%"=="3" goto install_deps
if "%choice%"=="4" goto test_full
if "%choice%"=="5" goto check_deps
if "%choice%"=="0" goto exit
echo Invalid choice, please try again
goto menu

:test_nextjs
echo.
echo [TEST 1] Testing Next.js development server...
echo ==============================================
echo.
echo Starting Next.js on http://localhost:3000
echo Press Ctrl+C to stop and return to menu
echo.
npm run dev
goto menu

:test_electron
echo.
echo [TEST 2] Testing Electron (make sure Next.js is running first)...
echo ================================================================
echo.
echo This will open Electron window pointing to http://localhost:3000
echo Make sure Next.js dev server is running in another terminal
echo.
pause
npm run electron
goto menu

:install_deps
echo.
echo [INSTALL] Installing Electron dependencies...
echo =============================================
echo.
npm install electron electron-builder electron-updater concurrently wait-on --save-dev
if errorlevel 1 (
    echo [ERROR] Installation failed
) else (
    echo [OK] Dependencies installed successfully
)
echo.
pause
goto menu

:test_full
echo.
echo [TEST 4] Testing full desktop development mode...
echo =================================================
echo.
echo This will run both Next.js and Electron together
echo.
npm run electron-dev
if errorlevel 1 (
    echo.
    echo [ERROR] Full test failed
    echo Try running tests 1 and 2 separately to identify the issue
)
echo.
pause
goto menu

:check_deps
echo.
echo [CHECK] Checking dependencies status...
echo =======================================
echo.

echo Checking Node.js...
node --version
if errorlevel 1 (
    echo [ERROR] Node.js not found
) else (
    echo [OK] Node.js found
)

echo.
echo Checking npm...
npm --version
if errorlevel 1 (
    echo [ERROR] npm not found
) else (
    echo [OK] npm found
)

echo.
echo Checking project dependencies...
if exist "node_modules" (
    echo [OK] node_modules directory exists
) else (
    echo [WARN] node_modules directory not found
    echo Run: npm install
)

echo.
echo Checking Electron...
npm list electron >nul 2>&1
if errorlevel 1 (
    echo [WARN] Electron not installed
    echo Run option 3 to install
) else (
    echo [OK] Electron installed
)

echo.
echo Checking concurrently...
npm list concurrently >nul 2>&1
if errorlevel 1 (
    echo [WARN] concurrently not installed
) else (
    echo [OK] concurrently installed
)

echo.
echo Checking wait-on...
npm list wait-on >nul 2>&1
if errorlevel 1 (
    echo [WARN] wait-on not installed
) else (
    echo [OK] wait-on installed
)

echo.
echo Checking package.json scripts...
if exist "package.json" (
    echo [OK] package.json exists
    findstr "electron-dev" package.json >nul
    if errorlevel 1 (
        echo [WARN] electron-dev script not found in package.json
    ) else (
        echo [OK] electron-dev script found
    )
) else (
    echo [ERROR] package.json not found
)

echo.
pause
goto menu

:exit
echo.
echo Exiting desktop test...
echo.
pause
exit /b 0
