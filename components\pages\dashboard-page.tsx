"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useCivitai } from "@/components/civitai/civitai-provider"
import { ImageIcon, Database, Palette, Activity, BarChart3, Calendar, Clock, TrendingUp } from "lucide-react"
import { useLanguage } from "@/contexts/language-context"
import { useEffect, useState } from "react"

// 定义下载历史记录类型
interface DownloadRecord {
  date: string
  count: number
  source: string
}

export function DashboardPage() {
  const { t } = useLanguage()
  const { images, metadata } = useCivitai()
  const [downloadHistory, setDownloadHistory] = useState<DownloadRecord[]>([])
  const [downloadsBySource, setDownloadsBySource] = useState<{ [key: string]: number }>({})
  const [downloadsByDate, setDownloadsByDate] = useState<{ [key: string]: number }>({})
  const [lastDownloadDate, setLastDownloadDate] = useState<string | null>(null)

  // 生成模拟的下载历史数据
  useEffect(() => {
    if (images.length === 0) return

    // 按来源分组
    const sourceMap: { [key: string]: number } = {}
    // 按日期分组
    const dateMap: { [key: string]: number } = {}
    // 历史记录
    const history: DownloadRecord[] = []

    // 处理图片数据
    images.forEach((img) => {
      // 提取日期 (YYYY-MM-DD)
      const date = new Date(img.createdAt).toISOString().split("T")[0]

      // 更新日期统计
      if (dateMap[date]) {
        dateMap[date]++
      } else {
        dateMap[date] = 1
      }

      // 更新来源统计 (使用username作为来源)
      const source = img.username || "unknown"
      if (sourceMap[source]) {
        sourceMap[source]++
      } else {
        sourceMap[source] = 1
      }

      // 添加到历史记录
      history.push({
        date,
        count: 1,
        source,
      })
    })

    // 找出最近的下载日期
    const dates = Object.keys(dateMap).sort().reverse()
    if (dates.length > 0) {
      setLastDownloadDate(dates[0])
    }

    // 更新状态
    setDownloadsBySource(sourceMap)
    setDownloadsByDate(dateMap)
    setDownloadHistory(history)
  }, [images])

  // 获取前5个最多下载的来源
  const topSources = Object.entries(downloadsBySource)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5)

  // 获取最近7天的下载数据
  const recentDates = Object.entries(downloadsByDate)
    .sort((a, b) => new Date(b[0]).getTime() - new Date(a[0]).getTime())
    .slice(0, 7)
    .reverse()

  return (
    <div className="space-y-6">
      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="frosted-card theme-border">
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2 text-slate-900 dark:text-slate-100">
              <div className="p-2 rounded-lg theme-gradient">
                <ImageIcon className="h-4 w-4 text-white" />
              </div>
              {t("dashboard.images")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold theme-text">{images.length}</div>
            <p className="text-xs text-slate-600 dark:text-slate-400">{t("dashboard.downloadedImages")}</p>
            <div className="mt-2 h-1 bg-slate-200 dark:bg-slate-700 rounded-full overflow-hidden">
              <div
                className="h-full theme-progress rounded-full"
                style={{ width: `${Math.min(images.length * 10, 100)}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>

        <Card className="frosted-card theme-border">
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2 text-slate-900 dark:text-slate-100">
              <div className="p-2 rounded-lg theme-gradient">
                <Database className="h-4 w-4 text-white" />
              </div>
              {t("dashboard.metadata")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold theme-text">{Object.keys(metadata).length}</div>
            <p className="text-xs text-slate-600 dark:text-slate-400">{t("dashboard.metadataRecords")}</p>
            <div className="mt-2 h-1 bg-slate-200 dark:bg-slate-700 rounded-full overflow-hidden">
              <div
                className="h-full theme-progress rounded-full"
                style={{ width: `${Math.min(Object.keys(metadata).length * 15, 100)}%` }}
              ></div>
            </div>
          </CardContent>
        </Card>

        <Card className="frosted-card theme-border">
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2 text-slate-900 dark:text-slate-100">
              <div className="p-2 rounded-lg theme-gradient">
                <Calendar className="h-4 w-4 text-white" />
              </div>
              最近下载
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold theme-text">
              {lastDownloadDate ? new Date(lastDownloadDate).toLocaleDateString() : "无记录"}
            </div>
            <p className="text-xs text-slate-600 dark:text-slate-400">上次下载日期</p>
            <div className="mt-2 flex items-center gap-1">
              <div className="h-2 w-2 rounded-full bg-green-500 animate-pulse"></div>
              <span className="text-xs theme-text">
                {lastDownloadDate
                  ? `${Math.floor((Date.now() - new Date(lastDownloadDate).getTime()) / (1000 * 60 * 60 * 24))}天前`
                  : "无记录"}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="frosted-card theme-border">
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2 text-slate-900 dark:text-slate-100">
              <div className="p-2 rounded-lg theme-gradient">
                <Activity className="h-4 w-4 text-white" />
              </div>
              {t("dashboard.status")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm font-medium text-green-600 dark:text-green-400">{t("dashboard.ready")}</div>
            <p className="text-xs text-slate-600 dark:text-slate-400">{t("dashboard.operational")}</p>
            <div className="mt-2 flex items-center gap-1">
              <div className="h-2 w-2 rounded-full bg-green-500 animate-pulse"></div>
              <span className="text-xs theme-text">在线</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 主要内容卡片 */}
      <Card className="frosted-card theme-border theme-shadow">
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center justify-between text-slate-900 dark:text-slate-100">
            <span className="flex items-center gap-2">
              <div className="p-2 rounded-lg theme-gradient">
                <Palette className="h-4 w-4 text-white" />
              </div>
              {t("dashboard.welcome")}
            </span>
          </CardTitle>
          <CardDescription className="text-slate-600 dark:text-slate-400">{t("dashboard.description")}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-sm font-medium text-slate-900 dark:text-slate-100 mb-3 flex items-center gap-2">
                <TrendingUp className="h-4 w-4 theme-text" />
                {t("dashboard.features")}
              </h3>
              <div className="flex flex-wrap gap-2">
                <Badge className="theme-primary text-xs">{t("feature.frostedGlass")}</Badge>
                <Badge className="theme-secondary text-xs">{t("feature.civitaiApi")}</Badge>
                <Badge className="theme-accent text-xs">{t("feature.metadataManagement")}</Badge>
                <Badge className="theme-primary text-xs">{t("feature.themeSystem")}</Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 历史下载记录图表 */}
      <Card className="frosted-card theme-border theme-shadow">
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center justify-between text-slate-900 dark:text-slate-100">
            <span className="flex items-center gap-2">
              <div className="p-2 rounded-lg theme-gradient">
                <BarChart3 className="h-4 w-4 text-white" />
              </div>
              历史下载记录
            </span>
          </CardTitle>
          <CardDescription className="text-slate-600 dark:text-slate-400">查看您的下载历史和统计数据</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 按日期的下载图表 */}
          <div>
            <h3 className="text-sm font-medium text-slate-900 dark:text-slate-100 mb-3 flex items-center gap-2">
              <Clock className="h-4 w-4 theme-text" />
              最近下载趋势
            </h3>

            {recentDates.length > 0 ? (
              <div className="h-40 flex items-end gap-2">
                {recentDates.map(([date, count], index) => {
                  // 计算柱状图高度百分比 (最大高度80%)
                  const maxCount = Math.max(...recentDates.map((item) => item[1]))
                  const heightPercent = maxCount > 0 ? (count / maxCount) * 80 : 0

                  return (
                    <div key={date} className="flex-1 flex flex-col items-center">
                      <div
                        className="w-full theme-gradient rounded-t-md transition-all hover:opacity-80"
                        style={{ height: `${heightPercent}%` }}
                      ></div>
                      <div className="text-xs mt-2 text-slate-600 dark:text-slate-400">
                        {new Date(date).toLocaleDateString(undefined, { month: "short", day: "numeric" })}
                      </div>
                      <div className="text-xs font-medium theme-text">{count}</div>
                    </div>
                  )
                })}
              </div>
            ) : (
              <div className="h-40 flex items-center justify-center border border-dashed border-slate-300 dark:border-slate-700 rounded-lg">
                <p className="text-sm text-slate-500 dark:text-slate-400">暂无下载记录</p>
              </div>
            )}
          </div>

          {/* 按来源的下载统计 */}
          <div className="border-t border-white/20 dark:border-white/10 pt-4">
            <h3 className="text-sm font-medium text-slate-900 dark:text-slate-100 mb-3 flex items-center gap-2">
              <TrendingUp className="h-4 w-4 theme-text" />
              热门下载来源
            </h3>

            {topSources.length > 0 ? (
              <div className="space-y-3">
                {topSources.map(([source, count]) => (
                  <div key={source} className="space-y-1">
                    <div className="flex justify-between text-xs">
                      <span className="font-medium theme-text">{source}</span>
                      <span className="text-slate-600 dark:text-slate-400">{count} 张图像</span>
                    </div>
                    <div className="h-1.5 bg-slate-200 dark:bg-slate-700 rounded-full overflow-hidden">
                      <div
                        className="h-full theme-progress rounded-full"
                        style={{ width: `${(count / images.length) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="h-20 flex items-center justify-center border border-dashed border-slate-300 dark:border-slate-700 rounded-lg">
                <p className="text-sm text-slate-500 dark:text-slate-400">暂无来源数据</p>
              </div>
            )}
          </div>

          {/* 标签统计 */}
          <div className="border-t border-white/20 dark:border-white/10 pt-4">
            <h3 className="text-sm font-medium text-slate-900 dark:text-slate-100 mb-3">常见标签</h3>
            <div className="flex flex-wrap gap-2">
              {Object.entries(
                Object.entries(metadata).reduce(
                  (tags, [_, meta]) => {
                    meta.modelInfo.tags.forEach((tag) => {
                      if (!tags[tag]) tags[tag] = 0
                      tags[tag]++
                    })
                    return tags
                  },
                  {} as { [key: string]: number },
                ),
              )
                .sort((a, b) => b[1] - a[1])
                .slice(0, 10)
                .map(([tag, count]) => (
                  <Badge key={tag} className="theme-secondary text-xs">
                    {tag} ({count})
                  </Badge>
                ))}

              {Object.keys(metadata).length === 0 && (
                <div className="w-full text-center py-2">
                  <p className="text-sm text-slate-500 dark:text-slate-400">暂无标签数据</p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
