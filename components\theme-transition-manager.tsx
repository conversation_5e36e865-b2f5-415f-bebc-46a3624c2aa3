"use client"

import * as React from "react"

export type AnimationType = "shimmer" | "ripple" | "fade" | "slide"

interface ThemeTransitionContextType {
  isTransitioning: boolean
  startTransition: (animationType?: AnimationType) => Promise<void>
  transitionDuration: number
  currentAnimation: AnimationType
  setCurrentAnimation: (animation: AnimationType) => void
}

const ThemeTransitionContext = React.createContext<ThemeTransitionContextType | null>(null)

export function useThemeTransition() {
  const context = React.useContext(ThemeTransitionContext)
  if (!context) {
    throw new Error("useThemeTransition must be used within ThemeTransitionProvider")
  }
  return context
}

export function ThemeTransitionProvider({ children }: { children: React.ReactNode }) {
  const [isTransitioning, setIsTransitioning] = React.useState(false)
  const [currentAnimation, setCurrentAnimation] = React.useState<AnimationType>("shimmer")
  const transitionDuration = 800 // ms

  // 加载保存的动画设置
  React.useEffect(() => {
    const savedAnimation = localStorage.getItem("theme-animation") as AnimationType
    if (savedAnimation && ["shimmer", "ripple", "fade", "slide"].includes(savedAnimation)) {
      setCurrentAnimation(savedAnimation)
    }
  }, [])

  // 保存动画设置
  const handleSetCurrentAnimation = React.useCallback((animation: AnimationType) => {
    setCurrentAnimation(animation)
    localStorage.setItem("theme-animation", animation)
    console.log("Animation setting saved:", animation)
  }, [])

  const createShimmerEffect = () => {
    const overlay = document.createElement("div")
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.4), transparent);
      z-index: 9999;
      pointer-events: none;
      animation: shimmerTransition 0.8s ease-in-out;
    `
    return overlay
  }

  const createRippleEffect = () => {
    const overlay = document.createElement("div")
    overlay.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: radial-gradient(circle, rgba(59, 130, 246, 0.4) 0%, transparent 70%);
      border-radius: 50%;
      z-index: 9999;
      pointer-events: none;
      transform: translate(-50%, -50%);
      animation: rippleTransition 0.8s ease-out;
    `
    return overlay
  }

  const createFadeEffect = () => {
    const overlay = document.createElement("div")
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(59, 130, 246, 0.3);
      z-index: 9999;
      pointer-events: none;
      opacity: 0;
      animation: fadeTransition 0.8s ease-in-out;
    `
    return overlay
  }

  const createSlideEffect = () => {
    const overlay = document.createElement("div")
    overlay.style.cssText = `
      position: fixed;
      top: -100%;
      left: 0;
      right: 0;
      height: 100%;
      background: linear-gradient(180deg, rgba(59, 130, 246, 0.4), rgba(147, 51, 234, 0.4));
      z-index: 9999;
      pointer-events: none;
      animation: slideTransition 0.8s ease-in-out;
    `
    return overlay
  }

  const startTransition = React.useCallback(
    async (animationType?: AnimationType) => {
      if (isTransitioning) return

      const effectType = animationType || currentAnimation
      console.log("Starting theme transition with animation:", effectType)
      setIsTransitioning(true)

      // 创建对应的动画效果
      let overlay: HTMLElement
      switch (effectType) {
        case "ripple":
          overlay = createRippleEffect()
          console.log("Creating ripple effect")
          break
        case "fade":
          overlay = createFadeEffect()
          console.log("Creating fade effect")
          break
        case "slide":
          overlay = createSlideEffect()
          console.log("Creating slide effect")
          break
        case "shimmer":
        default:
          overlay = createShimmerEffect()
          console.log("Creating shimmer effect")
          break
      }

      document.body.appendChild(overlay)

      // 添加对应的动画效果到玻璃元素
      const glassElements = document.querySelectorAll(".glass-effect, [data-sidebar], .frosted-sidebar, .frosted-card")
      glassElements.forEach((element) => {
        // 移除所有可能存在的动画类
        element.classList.remove(
          "theme-switching-shimmer",
          "theme-switching-ripple",
          "theme-switching-fade",
          "theme-switching-slide",
        )

        // 添加特定的动画类
        element.classList.add(`theme-switching-${effectType}`)
      })

      console.log(`Applied ${effectType} animation to ${glassElements.length} elements`)

      // 等待动画完成
      await new Promise((resolve) => setTimeout(resolve, transitionDuration))

      // 清理
      setTimeout(() => {
        if (document.body.contains(overlay)) {
          document.body.removeChild(overlay)
        }
        glassElements.forEach((element) => {
          // 移除所有动画类
          element.classList.remove(
            "theme-switching-shimmer",
            "theme-switching-ripple",
            "theme-switching-fade",
            "theme-switching-slide",
          )
        })
        setIsTransitioning(false)
        console.log("Theme transition completed")
      }, 200)
    },
    [isTransitioning, currentAnimation, transitionDuration],
  )

  const value = React.useMemo(
    () => ({
      isTransitioning,
      startTransition,
      transitionDuration,
      currentAnimation,
      setCurrentAnimation: handleSetCurrentAnimation,
    }),
    [isTransitioning, startTransition, transitionDuration, currentAnimation, handleSetCurrentAnimation],
  )

  return <ThemeTransitionContext.Provider value={value}>{children}</ThemeTransitionContext.Provider>
}
