{"appId": "com.civitai.explorer", "productName": "Civitai Explorer", "directories": {"output": "dist"}, "files": ["out/**/*", "electron/**/*", "public/icon.*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "public/", "to": "public/", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64"]}], "icon": "public/icon.ico", "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Civitai Explorer", "include": "electron/installer.nsh"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "public/icon.icns", "category": "public.app-category.graphics-design", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "dmg": {"title": "Civitai Explorer", "icon": "public/icon.icns", "background": "public/dmg-background.png", "contents": [{"x": 410, "y": 150, "type": "link", "path": "/Applications"}, {"x": 130, "y": 150, "type": "file"}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "public/icon.png", "category": "Graphics", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "publish": {"provider": "github", "owner": "your-username", "repo": "civitai-explorer"}}