@echo off

:: =============================================================================
:: Civitai Explorer Desktop Development Script
:: =============================================================================

title Civitai Explorer - Desktop Development

echo.
echo Civitai Explorer Desktop Development
echo ====================================
echo.

echo This script will start the application in desktop development mode.
echo.

:: Check if package.json exists
if not exist "package.json" (
    echo [ERROR] package.json not found
    echo Please run this script in the project root directory
    pause
    exit /b 1
)

:: Check if node_modules exists
if not exist "node_modules" (
    echo [INFO] node_modules not found, installing dependencies...
    npm install
    if errorlevel 1 (
        echo [ERROR] Failed to install dependencies
        pause
        exit /b 1
    )
)

:: Check if Electron dependencies are installed
echo [INFO] Checking Electron dependencies...
npm list electron >nul 2>&1
if errorlevel 1 (
    echo [INFO] Installing Electron dependencies...
    npm install electron electron-builder electron-updater concurrently wait-on --save-dev
    if errorlevel 1 (
        echo [ERROR] Failed to install Electron dependencies
        pause
        exit /b 1
    )
    echo [OK] Electron dependencies installed
)

:: Check if concurrently is available
npm list concurrently >nul 2>&1
if errorlevel 1 (
    echo [INFO] Installing concurrently...
    npm install concurrently --save-dev
    if errorlevel 1 (
        echo [ERROR] Failed to install concurrently
        pause
        exit /b 1
    )
)

:: Check if wait-on is available
npm list wait-on >nul 2>&1
if errorlevel 1 (
    echo [INFO] Installing wait-on...
    npm install wait-on --save-dev
    if errorlevel 1 (
        echo [ERROR] Failed to install wait-on
        pause
        exit /b 1
    )
)

echo.
echo [INFO] All dependencies are ready
echo [INFO] Starting desktop development mode...
echo.
echo PROCESS:
echo 1. Starting Next.js development server on http://localhost:3000
echo 2. Waiting for server to be ready
echo 3. Launching Electron window
echo.
echo TIP: Press Ctrl+C to stop both servers
echo.

:: Start both Next.js dev server and Electron
echo [EXEC] Running: npm run electron-dev
npm run electron-dev

if errorlevel 1 (
    echo.
    echo [ERROR] Failed to start desktop development mode
    echo.
    echo TROUBLESHOOTING:
    echo 1. Check if port 3000 is already in use
    echo 2. Try running: npm run dev (to test Next.js server)
    echo 3. Try running: npm run electron (to test Electron)
    echo.
    pause
    exit /b 1
)

echo.
echo Desktop development session ended
pause
