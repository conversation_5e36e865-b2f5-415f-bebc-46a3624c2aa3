@echo off

:: =============================================================================
:: Civitai Explorer Desktop Development Script
:: =============================================================================

title Civitai Explorer - Desktop Development

echo.
echo Civitai Explorer Desktop Development
echo ====================================
echo.

echo This script will start the application in desktop development mode.
echo It will:
echo 1. Start the Next.js development server
echo 2. Launch Electron when the server is ready
echo.

:: Check if Electron is installed
npm list electron >nul 2>&1
if errorlevel 1 (
    echo [INFO] Electron not found, installing...
    npm install electron electron-builder electron-updater concurrently wait-on --save-dev
    if errorlevel 1 (
        echo [ERROR] Failed to install Electron
        pause
        exit /b 1
    )
)

echo [INFO] Starting desktop development mode...
echo.
echo TIP: The Electron window will open automatically when ready
echo Press Ctrl+C to stop both servers
echo.

:: Start both Next.js dev server and Electron
npm run electron-dev

echo.
echo Desktop development session ended
pause
