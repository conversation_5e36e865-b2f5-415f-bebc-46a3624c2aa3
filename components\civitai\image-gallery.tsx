"use client"

import { useState } from "react"
import { useCivitai } from "./civitai-provider"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Trash2, MoreVertical, Download, Info, Search, AlertCircle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { MetadataViewer } from "./metadata-viewer"
import { useLanguage } from "@/contexts/language-context"

export function ImageGallery() {
  const { images, metadata, deleteImage, clearAllData } = useCivitai()
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedImage, setSelectedImage] = useState<string | null>(null)
  const [showMetadata, setShowMetadata] = useState(false)
  const { t } = useLanguage()

  // Filter images based on search term
  const filteredImages = images.filter(
    (img) =>
      img.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      img.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      metadata[img.id]?.modelInfo.name.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleDeleteImage = (id: string) => {
    deleteImage(id)
  }

  const handleViewMetadata = (id: string) => {
    setSelectedImage(id)
    setShowMetadata(true)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-slate-500" />
          <Input
            placeholder={t("gallery.search")}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 frosted-glass"
          />
        </div>
        <Button
          variant="destructive"
          onClick={() => {
            if (confirm(t("gallery.confirmDelete"))) {
              clearAllData()
            }
          }}
          className="whitespace-nowrap"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          {t("gallery.clearAll")}
        </Button>
      </div>

      {images.length === 0 ? (
        <Alert className="frosted-glass">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{t("gallery.noImages")}</AlertDescription>
        </Alert>
      ) : filteredImages.length === 0 ? (
        <Alert className="frosted-glass">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{t("gallery.noMatches")}</AlertDescription>
        </Alert>
      ) : (
        <div className="image-grid">
          {filteredImages.map((image) => (
            <div key={image.id} className="image-card frosted-card">
              <img src={image.url || "/placeholder.svg"} alt={image.name} />
              <div className="image-overlay">
                <h3 className="font-medium">{image.name}</h3>
                <p className="text-sm opacity-80">By {image.username}</p>
              </div>
              <div className="image-actions">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      size="icon"
                      variant="secondary"
                      className="h-8 w-8 rounded-full bg-white/20 backdrop-blur-md"
                    >
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="frosted-glass">
                    <DropdownMenuItem onClick={() => handleViewMetadata(image.id)}>
                      <Info className="mr-2 h-4 w-4" />
                      {t("gallery.viewMetadata")}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => window.open(image.url, "_blank")}>
                      <Download className="mr-2 h-4 w-4" />
                      {t("gallery.downloadImage")}
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => handleDeleteImage(image.id)}
                      className="text-red-500 focus:text-red-500"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      {t("button.delete")}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          ))}
        </div>
      )}

      <Dialog open={showMetadata} onOpenChange={setShowMetadata}>
        <DialogContent className="max-w-3xl frosted-glass">
          <DialogHeader>
            <DialogTitle>{t("metadata.imageMetadata")}</DialogTitle>
            <DialogDescription>{t("metadata.detailedInfo")}</DialogDescription>
          </DialogHeader>
          {selectedImage && metadata[selectedImage] && <MetadataViewer metadata={metadata[selectedImage]} />}
        </DialogContent>
      </Dialog>
    </div>
  )
}
