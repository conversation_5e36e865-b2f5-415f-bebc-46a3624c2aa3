// Civitai API 文档: https://github.com/civitai/civitai/wiki/REST-API-Reference

// API基础URL
const CIVITAI_API_BASE = "https://civitai.com/api/v1"

// API密钥类型
export type CivitaiApiKey = string | null

// API响应类型
export interface CivitaiApiResponse<T> {
  items: T[]
  metadata: {
    totalItems: number
    currentPage: number
    pageSize: number
    totalPages: number
    nextPage?: string
    prevPage?: string
  }
}

// API限制信息
export interface ApiLimits {
  maxPerPage: number
  maxTotal: number
  hasApiKey: boolean
}

/**
 * 获取API限制信息
 */
export function getApiLimits(apiKey?: CivitaiApiKey): ApiLimits {
  return {
    maxPerPage: apiKey ? 200 : 20, // 有API密钥时每页最多200，否则20
    maxTotal: apiKey ? 10000 : 100, // 有API密钥时最多10000，否则100
    hasApiKey: !!apiKey,
  }
}

/**
 * 创建API请求头，包括可选的API密钥
 */
function createHeaders(apiKey?: CivitaiApiKey): HeadersInit {
  const headers: HeadersInit = {
    "Content-Type": "application/json",
    "User-Agent": "CivitaiExplorer/1.0.0",
  }

  if (apiKey) {
    headers["Authorization"] = `Bearer ${apiKey}`
  }

  return headers
}

/**
 * 处理API响应
 */
async function handleResponse<T>(response: Response): Promise<CivitaiApiResponse<T>> {
  if (!response.ok) {
    // 处理不同的错误状态
    if (response.status === 401) {
      throw new Error("API密钥无效或已过期")
    } else if (response.status === 403) {
      throw new Error("访问被拒绝，请检查API密钥权限")
    } else if (response.status === 429) {
      throw new Error("请求过于频繁，请稍后再试")
    } else if (response.status === 404) {
      throw new Error("未找到请求的资源")
    }

    // 尝试获取错误信息
    try {
      const errorData = await response.json()
      throw new Error(errorData.message || `API错误: ${response.status}`)
    } catch (e) {
      throw new Error(`API错误: ${response.status} ${response.statusText}`)
    }
  }

  return (await response.json()) as CivitaiApiResponse<T>
}

/**
 * 添加延迟以避免速率限制
 */
function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

/**
 * 批量获取图像（支持分页）
 */
export async function getBatchImages(
  params: {
    username?: string
    modelId?: string
    modelVersionId?: string
    totalLimit?: number
    onProgress?: (current: number, total: number) => void
  },
  apiKey?: CivitaiApiKey,
): Promise<{ items: any[]; totalFetched: number; totalAvailable: number }> {
  const limits = getApiLimits(apiKey)
  const totalLimit = Math.min(params.totalLimit || 50, limits.maxTotal)
  const pageSize = Math.min(limits.maxPerPage, 100) // 每页最多100，避免请求过大

  const allItems: any[] = []
  let currentPage = 1
  let totalAvailable = 0
  let hasMore = true

  console.log(`开始批量下载，目标数量: ${totalLimit}, 每页: ${pageSize}, API密钥: ${apiKey ? "已设置" : "未设置"}`)

  while (hasMore && allItems.length < totalLimit) {
    try {
      // 构建URL参数
      const urlParams = new URLSearchParams({
        limit: pageSize.toString(),
        page: currentPage.toString(),
      })

      if (params.username) {
        urlParams.append("username", params.username)
      }
      if (params.modelId) {
        urlParams.append("modelId", params.modelId)
      }
      if (params.modelVersionId) {
        urlParams.append("modelVersionId", params.modelVersionId)
      }

      const url = `${CIVITAI_API_BASE}/images?${urlParams.toString()}`
      console.log(`请求第 ${currentPage} 页: ${url}`)

      const response = await fetch(url, {
        method: "GET",
        headers: createHeaders(apiKey),
      })

      const data = await handleResponse<any>(response)
      console.log(`第 ${currentPage} 页响应:`, {
        itemsCount: data.items?.length || 0,
        metadata: data.metadata,
      })

      if (currentPage === 1) {
        totalAvailable = data.metadata?.totalItems || data.items?.length || 0
        console.log(`总共可用图像: ${totalAvailable}`)

        // 立即更新进度，显示总数
        if (params.onProgress) {
          params.onProgress(0, Math.min(totalLimit, totalAvailable))
        }
      }

      if (!data.items || data.items.length === 0) {
        console.log("没有更多图像")
        hasMore = false
        break
      }

      // 添加到结果中，但不超过限制
      const remainingSlots = totalLimit - allItems.length
      const itemsToAdd = data.items.slice(0, remainingSlots)
      allItems.push(...itemsToAdd)

      console.log(`第 ${currentPage} 页获取了 ${itemsToAdd.length} 张图像，总计: ${allItems.length}`)

      // 调用进度回调
      if (params.onProgress) {
        params.onProgress(allItems.length, Math.min(totalLimit, totalAvailable))
      }

      // 检查是否还有更多页面
      const hasMorePages =
        data.metadata?.currentPage < data.metadata?.totalPages || (!data.metadata && data.items.length === pageSize)

      hasMore =
        hasMorePages &&
        allItems.length < totalLimit &&
        allItems.length < totalAvailable &&
        data.items.length === pageSize

      console.log(
        `页面检查: hasMorePages=${hasMorePages}, allItems.length=${allItems.length}, totalLimit=${totalLimit}, totalAvailable=${totalAvailable}, itemsLength=${data.items.length}, pageSize=${pageSize}`,
      )

      if (hasMore) {
        currentPage++
        // 添加延迟以避免速率限制
        const delayTime = apiKey ? 200 : 1000 // 有API密钥时延迟200ms，否则1000ms
        console.log(`等待 ${delayTime}ms 后继续下一页...`)
        await delay(delayTime)
      }
    } catch (error) {
      console.error(`获取第 ${currentPage} 页时出错:`, error)

      // 如果是速率限制错误，等待更长时间后重试
      if (error instanceof Error && error.message.includes("频繁")) {
        console.log("遇到速率限制，等待10秒后重试...")
        await delay(10000)
        continue
      }

      // 其他错误则抛出
      throw error
    }
  }

  console.log(`批量下载完成，获取了 ${allItems.length} 张图像`)

  return {
    items: allItems,
    totalFetched: allItems.length,
    totalAvailable,
  }
}

/**
 * 通过用户名获取图像（支持批量）
 */
export async function getImagesByUsername(
  username: string,
  totalLimit = 50,
  apiKey?: CivitaiApiKey,
  onProgress?: (current: number, total: number) => void,
): Promise<{ items: any[]; totalFetched: number; totalAvailable: number }> {
  return getBatchImages({ username, totalLimit, onProgress }, apiKey)
}

/**
 * 通过模型ID获取图像（支持批量）
 */
export async function getImagesByModelId(
  modelId: string,
  totalLimit = 50,
  apiKey?: CivitaiApiKey,
  onProgress?: (current: number, total: number) => void,
): Promise<{ items: any[]; totalFetched: number; totalAvailable: number }> {
  return getBatchImages({ modelId, totalLimit, onProgress }, apiKey)
}

/**
 * 通过模型版本ID获取图像（支持批量）
 */
export async function getImagesByModelVersionId(
  modelVersionId: string,
  totalLimit = 50,
  apiKey?: CivitaiApiKey,
  onProgress?: (current: number, total: number) => void,
): Promise<{ items: any[]; totalFetched: number; totalAvailable: number }> {
  return getBatchImages({ modelVersionId, totalLimit, onProgress }, apiKey)
}

/**
 * 获取图像详情
 */
export async function getImageDetails(imageId: string, apiKey?: CivitaiApiKey): Promise<any> {
  const url = `${CIVITAI_API_BASE}/images/${imageId}`
  const response = await fetch(url, {
    method: "GET",
    headers: createHeaders(apiKey),
  })

  const data = await handleResponse<any>(response)
  return data.items?.[0] || data
}

/**
 * 获取模型详情
 */
export async function getModelDetails(modelId: string, apiKey?: CivitaiApiKey): Promise<any> {
  const url = `${CIVITAI_API_BASE}/models/${modelId}`
  const response = await fetch(url, {
    method: "GET",
    headers: createHeaders(apiKey),
  })

  return handleResponse(response)
}

/**
 * 获取模型版本详情
 */
export async function getModelVersionDetails(modelVersionId: string, apiKey?: CivitaiApiKey): Promise<any> {
  const url = `${CIVITAI_API_BASE}/model-versions/${modelVersionId}`
  const response = await fetch(url, {
    method: "GET",
    headers: createHeaders(apiKey),
  })

  return handleResponse(response)
}

/**
 * 将Civitai API响应转换为应用内部使用的格式
 */
export function convertApiImageToAppImage(apiImage: any): any {
  return {
    id: apiImage.id?.toString() || `temp-${Date.now()}`,
    url: apiImage.url || "",
    name: apiImage.meta?.prompt?.slice(0, 50) || `Image ${apiImage.id}`,
    createdAt: apiImage.createdAt || new Date().toISOString(),
    modelId: apiImage.modelId?.toString() || "",
    modelVersionId: apiImage.modelVersionId?.toString() || "",
    username: apiImage.username || "unknown",
  }
}

/**
 * 将Civitai API响应转换为应用内部使用的元数据格式
 */
export function convertApiImageToAppMetadata(apiImage: any): any {
  return {
    id: apiImage.id?.toString() || `temp-${Date.now()}`,
    modelInfo: {
      name: apiImage.meta?.Model || "Unknown Model",
      description: apiImage.meta?.prompt || "",
      type: apiImage.meta?.["Model type"] || "Unknown",
      nsfw: apiImage.nsfw || false,
      tags: apiImage.tags || [],
    },
    imageInfo: {
      width: apiImage.width || 0,
      height: apiImage.height || 0,
      hash: apiImage.hash || "",
      size: apiImage.sizeKB ? apiImage.sizeKB * 1024 : 0,
    },
    generationParams: {
      prompt: apiImage.meta?.prompt || "",
      negativePrompt: apiImage.meta?.negativePrompt || "",
      steps: Number.parseInt(apiImage.meta?.steps) || 0,
      sampler: apiImage.meta?.sampler || "",
      cfgScale: apiImage.meta?.cfgScale?.toString() || "0",
      seed: Number.parseInt(apiImage.meta?.seed) || 0,
    },
    user: {
      username: apiImage.username || "unknown",
      profilePicture:
        apiImage.profilePicture || `https://ui-avatars.com/api/?name=${apiImage.username || "U"}&background=random`,
    },
  }
}
