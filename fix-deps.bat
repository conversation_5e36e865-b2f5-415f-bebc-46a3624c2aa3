@echo off

:: =============================================================================
:: Civitai Explorer Dependency Fix Script
:: =============================================================================

title Civitai Explorer - Dependency Fix

echo.
echo Civitai Explorer Dependency Fix
echo ================================
echo.

:: Check project files
if not exist "package.json" (
    echo ERROR: Please run this script in the project root directory
    pause
    exit /b 1
)

echo This script will fix dependency conflicts by:
echo 1. Removing node_modules directory
echo 2. Removing lock files
echo 3. Installing dependencies with compatibility flags
echo.

set /p confirm="Do you want to continue? (y/n): "
if /i not "%confirm%"=="y" (
    echo Operation cancelled
    pause
    exit /b 0
)

echo.
echo [STEP 1] Cleaning existing installation...
echo ==========================================

:: Remove node_modules
if exist "node_modules" (
    echo Removing node_modules directory...
    rmdir /s /q "node_modules"
    echo Done.
) else (
    echo node_modules not found, skipping...
)

:: Remove lock files
if exist "package-lock.json" (
    echo Removing package-lock.json...
    del "package-lock.json"
    echo Done.
)

if exist "pnpm-lock.yaml" (
    echo Removing pnpm-lock.yaml...
    del "pnpm-lock.yaml"
    echo Done.
)

if exist "yarn.lock" (
    echo Removing yarn.lock...
    del "yarn.lock"
    echo Done.
)

:: Clean Next.js cache
if exist ".next" (
    echo Removing .next cache...
    rmdir /s /q ".next"
    echo Done.
)

echo.
echo [STEP 2] Installing dependencies...
echo ===================================

:: Check which package manager to use
pnpm --version >nul 2>&1
if errorlevel 1 (
    echo Using npm...
    echo.
    
    echo Trying: npm install --legacy-peer-deps
    npm install --legacy-peer-deps
    
    if errorlevel 1 (
        echo.
        echo Legacy install failed, trying: npm install --force
        npm install --force
        
        if errorlevel 1 (
            echo.
            echo Force install failed, trying: npm install --no-optional
            npm install --no-optional
            
            if errorlevel 1 (
                echo.
                echo [ERROR] All installation methods failed
                echo.
                echo Possible solutions:
                echo 1. Check your internet connection
                echo 2. Clear npm cache: npm cache clean --force
                echo 3. Update Node.js to the latest LTS version
                echo 4. Try running as administrator
                echo.
                pause
                exit /b 1
            )
        )
    )
) else (
    echo Using pnpm...
    echo.
    
    echo Trying: pnpm install --legacy-peer-deps
    pnpm install --legacy-peer-deps
    
    if errorlevel 1 (
        echo.
        echo Legacy install failed, trying: pnpm install --force
        pnpm install --force
        
        if errorlevel 1 (
            echo.
            echo Force install failed, trying: pnpm install --no-optional
            pnpm install --no-optional
            
            if errorlevel 1 (
                echo.
                echo [ERROR] All installation methods failed
                echo.
                echo Possible solutions:
                echo 1. Check your internet connection
                echo 2. Clear pnpm cache: pnpm store prune
                echo 3. Update Node.js to the latest LTS version
                echo 4. Try running as administrator
                echo.
                pause
                exit /b 1
            )
        )
    )
)

echo.
echo [SUCCESS] Dependencies installed successfully!
echo =============================================
echo.
echo You can now run the project using:
echo - quick.bat (for quick start)
echo - start.bat (for full menu)
echo.
echo Or manually with:
pnpm --version >nul 2>&1
if errorlevel 1 (
    echo - npm run dev
) else (
    echo - pnpm dev
)
echo.

pause
