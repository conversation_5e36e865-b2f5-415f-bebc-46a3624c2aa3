@echo off

:: =============================================================================
:: Civitai Explorer Theme Fix Script
:: =============================================================================

title Civitai Explorer - Theme Fix

echo.
echo Civitai Explorer Theme Fix
echo ==========================
echo.

echo PROBLEM FIXED: CSS variable syntax error
echo.
echo The issue was that CSS variables contained incomplete HSL values
echo and the CSS was trying to wrap them in hsl() again.
echo.
echo SOLUTION: Changed variables to complete HSL values
echo and updated CSS to use direct variable references.
echo.

:: Clear Next.js cache
echo [STEP 1] Clearing Next.js cache...
if exist ".next" (
    echo Removing .next directory...
    rmdir /s /q ".next"
    echo Done.
) else (
    echo .next directory not found, skipping...
)

echo.
echo [SUCCESS] Theme fix applied!
echo.
echo WHAT SHOULD WORK NOW:
echo - All theme buttons should display correct colors
echo - Theme cards should have colored borders  
echo - Theme badges should use theme colors
echo - Theme links should use theme colors
echo - Theme switching should work for all components
echo - Both simplified and original theme tests should work
echo.

pause

echo.
echo Please restart your development server now:
echo 1. Stop current server (Ctrl+C in terminal)
echo 2. Run: quick.bat or start.bat
echo 3. Go to Dashboard theme test sections
echo 4. Test BOTH theme areas - they should both work now!
echo.
echo This should fix all theme color issues!
echo.

pause
