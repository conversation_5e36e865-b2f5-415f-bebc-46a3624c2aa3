"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Key, Eye, EyeOff, Save, Trash } from "lucide-react"
import { useCivitai } from "./civitai-provider"
import { useLanguage } from "@/contexts/language-context"

export function ApiKeySettings() {
  const { apiKey, setApiKey } = useCivitai()
  const [inputKey, setInputKey] = useState(apiKey || "")
  const [showKey, setShowKey] = useState(false)
  const { t } = useLanguage()

  const handleSaveKey = () => {
    setApiKey(inputKey.trim() || null)
  }

  const handleClearKey = () => {
    setInputKey("")
    setApiKey(null)
  }

  return (
    <Card className="frosted-card theme-border theme-shadow">
      <CardHeader>
        <CardTitle className="text-base flex items-center gap-2 theme-text">
          <div className="p-2 rounded-lg theme-gradient">
            <Key className="h-4 w-4 text-white" />
          </div>
          Civitai API 密钥
        </CardTitle>
        <CardDescription>设置您的 Civitai API 密钥以访问更多功能</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="api-key" className="theme-text">
            API 密钥
          </Label>
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Input
                id="api-key"
                type={showKey ? "text" : "password"}
                placeholder="输入您的 Civitai API 密钥"
                value={inputKey}
                onChange={(e) => setInputKey(e.target.value)}
                className="pr-10 theme-border theme-ring"
              />
              <button
                type="button"
                onClick={() => setShowKey(!showKey)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-500 hover:text-slate-700 dark:hover:text-slate-300"
              >
                {showKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
            <Button onClick={handleSaveKey} className="theme-primary theme-primary-hover">
              <Save className="h-4 w-4 mr-2" />
              保存
            </Button>
            <Button onClick={handleClearKey} variant="outline" className="theme-border">
              <Trash className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="text-sm space-y-2">
          <h3 className="font-medium theme-text">如何获取 API 密钥</h3>
          <ol className="list-decimal list-inside space-y-1 text-xs text-slate-700 dark:text-slate-300">
            <li>
              登录到{" "}
              <a href="https://civitai.com" target="_blank" rel="noopener noreferrer" className="theme-text underline">
                Civitai
              </a>{" "}
              网站
            </li>
            <li>点击右上角的个人头像，然后选择"账户设置"</li>
            <li>在左侧菜单中选择"API 密钥"</li>
            <li>点击"创建新密钥"按钮</li>
            <li>为密钥命名并设置权限</li>
            <li>复制生成的密钥并粘贴到上面的输入框中</li>
          </ol>
        </div>

        <div className="p-3 rounded-lg theme-accent theme-border">
          <p className="text-xs">
            <strong>注意：</strong> API 密钥是可选的，但某些功能可能需要 API 密钥才能访问。您的 API
            密钥仅存储在本地设备上，不会发送到任何其他服务器。
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
