"use client"

import { useState } from "react"
import { useCivitai } from "./civitai-provider"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Slider } from "@/components/ui/slider"
import {
  Loader2,
  AlertCircle,
  Download,
  Check,
  User,
  Hash,
  GitBranch,
  Info,
  Key,
  BarChart3,
  ImageIcon,
  Clock,
  TrendingUp,
} from "lucide-react"
import { useLanguage } from "@/contexts/language-context"

export function CivitaiDownloader() {
  const { downloadImage, isLoading, error, apiKey, downloadProgress, getApiLimits } = useCivitai()
  const [username, setUsername] = useState("")
  const [modelId, setModelId] = useState("")
  const [modelVersionId, setModelVersionId] = useState("")
  const [downloadLimit, setDownloadLimit] = useState([20])
  const [success, setSuccess] = useState(false)
  const [downloadStats, setDownloadStats] = useState<{
    startTime: number
    endTime: number | null
    totalDownloaded: number
    newImages: number
    duplicates: number
  } | null>(null)
  const { t } = useLanguage()

  const limits = getApiLimits()
  const maxLimit = limits.maxTotal

  const handleDownload = async (type: "username" | "modelId" | "versionId") => {
    try {
      if (type === "username" && !username.trim()) {
        throw new Error("用户名不能为空")
      }

      if (type === "modelId" && !modelId.trim()) {
        throw new Error("模型ID不能为空")
      }

      if (type === "versionId" && !modelVersionId.trim()) {
        throw new Error("模型版本ID不能为空")
      }

      // 重置状态
      setSuccess(false)
      setDownloadStats(null)

      // 记录开始时间和初始化统计
      const startTime = Date.now()
      setDownloadStats({
        startTime,
        endTime: null,
        totalDownloaded: 0,
        newImages: 0,
        duplicates: 0,
      })

      console.log(`开始下载，类型: ${type}, 限制: ${downloadLimit[0]}, API密钥: ${apiKey ? "已设置" : "未设置"}`)

      // 下载完成的回调
      const onComplete = (stats: { total: number; new: number; duplicates: number }) => {
        const endTime = Date.now()
        setDownloadStats({
          startTime,
          endTime,
          totalDownloaded: stats.total,
          newImages: stats.new,
          duplicates: stats.duplicates,
        })
        console.log("下载完成统计:", stats)
      }

      await downloadImage({
        username: type === "username" ? username.trim() : undefined,
        modelId: type === "modelId" ? modelId.trim() : undefined,
        modelVersionId: type === "versionId" ? modelVersionId.trim() : undefined,
        limit: downloadLimit[0],
        onComplete,
      })

      setSuccess(true)
      setTimeout(() => setSuccess(false), 5000)
    } catch (err) {
      console.error("Download error:", err)
      // 如果出错，也记录结束时间
      if (downloadStats) {
        setDownloadStats({
          ...downloadStats,
          endTime: Date.now(),
        })
      }
    }
  }

  // 计算下载用时
  const getElapsedTime = () => {
    if (!downloadStats) return ""
    const elapsed = (downloadStats.endTime || Date.now()) - downloadStats.startTime

    if (elapsed < 1000) return `${elapsed}ms`

    const seconds = Math.floor(elapsed / 1000)
    if (seconds < 60) return `${seconds}秒`

    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}分${remainingSeconds}秒`
  }

  // 计算下载速度
  const getDownloadSpeed = () => {
    if (!downloadStats || !downloadStats.endTime || downloadStats.totalDownloaded === 0) return ""

    const elapsed = (downloadStats.endTime - downloadStats.startTime) / 1000 // 秒
    if (elapsed <= 0) return ""

    const speed = downloadStats.totalDownloaded / elapsed
    return `${speed.toFixed(2)}张/秒`
  }

  // 计算进度百分比
  const getProgressPercentage = () => {
    if (!downloadProgress || downloadProgress.total === 0) return 0
    return Math.round((downloadProgress.current / downloadProgress.total) * 100)
  }

  return (
    <div className="space-y-6">
      {/* API限制信息 */}
      <Card className="frosted-card theme-border">
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2 theme-text">
            <Info className="h-4 w-4" />
            API 使用限制
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="text-center p-3 rounded-lg theme-accent theme-border">
              <div className="font-medium theme-text">每页最大</div>
              <div className="text-lg font-bold">{limits.maxPerPage}</div>
            </div>
            <div className="text-center p-3 rounded-lg theme-accent theme-border">
              <div className="font-medium theme-text">总计最大</div>
              <div className="text-lg font-bold">{limits.maxTotal}</div>
            </div>
            <div className="text-center p-3 rounded-lg theme-accent theme-border">
              <div className="font-medium theme-text">API 状态</div>
              <div className="text-sm font-bold flex items-center justify-center gap-1">
                {limits.hasApiKey ? (
                  <>
                    <Key className="h-3 w-3 text-green-500" />
                    <span className="text-green-600 dark:text-green-400">已认证</span>
                  </>
                ) : (
                  <>
                    <Key className="h-3 w-3 text-orange-500" />
                    <span className="text-orange-600 dark:text-orange-400">未认证</span>
                  </>
                )}
              </div>
            </div>
          </div>
          {!limits.hasApiKey && (
            <Alert className="mt-4 theme-accent theme-border">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                没有 API 密钥时，下载功能受限。请在设置页面添加 Civitai API 密钥以获得完整功能。
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* 下载数量设置 */}
      <Card className="frosted-card theme-border">
        <CardHeader>
          <CardTitle className="text-base theme-text">下载设置</CardTitle>
          <CardDescription>设置要下载的图像数量</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="theme-text">下载数量</Label>
              <span className="text-sm font-medium theme-text">{downloadLimit[0]} 张</span>
            </div>
            <Slider
              value={downloadLimit}
              onValueChange={setDownloadLimit}
              max={maxLimit}
              min={1}
              step={1}
              className="w-full"
              disabled={isLoading}
            />
            <div className="flex justify-between text-xs text-slate-500 dark:text-slate-400">
              <span>1</span>
              <span>{maxLimit}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 下载进度和统计 */}
      {(downloadProgress || downloadStats || isLoading) && (
        <Card className="frosted-card theme-border theme-shadow">
          <CardHeader className="pb-2">
            <CardTitle className="text-base flex items-center gap-2 theme-text">
              <BarChart3 className="h-4 w-4" />
              下载进度与统计
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 进度条 */}
            {(downloadProgress || isLoading) && (
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin theme-text" />
                    <span className="theme-text">下载进度</span>
                  </span>
                  <span className="font-medium theme-text">
                    {downloadProgress ? `${downloadProgress.current} / ${downloadProgress.total}` : "准备中..."}
                  </span>
                </div>

                <Progress
                  value={downloadProgress ? getProgressPercentage() : 0}
                  className="w-full h-3 bg-slate-200 dark:bg-slate-700"
                />

                <div className="flex justify-between text-xs">
                  <span className="text-slate-500 dark:text-slate-400">
                    {downloadProgress ? `${getProgressPercentage()}%` : "0%"}
                  </span>
                  <span className="text-slate-500 dark:text-slate-400">
                    {downloadProgress && downloadProgress.total > 0
                      ? `剩余 ${downloadProgress.total - downloadProgress.current} 张`
                      : "计算中..."}
                  </span>
                </div>
              </div>
            )}

            {/* 下载统计 */}
            {downloadStats && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3 pt-2 border-t border-white/20 dark:border-white/10">
                <div className="p-3 rounded-lg theme-accent theme-border">
                  <div className="text-xs text-slate-600 dark:text-slate-400 flex items-center gap-1 mb-1">
                    <ImageIcon className="h-3 w-3" />
                    总下载
                  </div>
                  <div className="text-xl font-bold theme-text">{downloadStats.totalDownloaded}</div>
                </div>

                <div className="p-3 rounded-lg theme-accent theme-border">
                  <div className="text-xs text-slate-600 dark:text-slate-400 flex items-center gap-1 mb-1">
                    <Check className="h-3 w-3" />
                    新增图像
                  </div>
                  <div className="text-xl font-bold theme-text">{downloadStats.newImages}</div>
                </div>

                <div className="p-3 rounded-lg theme-accent theme-border">
                  <div className="text-xs text-slate-600 dark:text-slate-400 flex items-center gap-1 mb-1">
                    <Clock className="h-3 w-3" />
                    用时
                  </div>
                  <div className="text-sm font-bold theme-text">{getElapsedTime()}</div>
                </div>

                <div className="p-3 rounded-lg theme-accent theme-border">
                  <div className="text-xs text-slate-600 dark:text-slate-400 flex items-center gap-1 mb-1">
                    <TrendingUp className="h-3 w-3" />
                    速度
                  </div>
                  <div className="text-sm font-bold theme-text">{getDownloadSpeed()}</div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 主下载界面 */}
      <Card className="frosted-card theme-border theme-shadow">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 theme-text">
            <div className="p-2 rounded-lg theme-gradient">
              <Download className="h-4 w-4 text-white" />
            </div>
            {t("downloader.title")}
          </CardTitle>
          <CardDescription>从 Civitai 批量下载图像和元数据</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="username" className="w-full">
            <TabsList className="frosted-glass theme-border grid w-full grid-cols-3">
              <TabsTrigger value="username" className="theme-ring">
                <User className="mr-2 h-4 w-4" />
                {t("downloader.byUsername")}
              </TabsTrigger>
              <TabsTrigger value="modelId" className="theme-ring">
                <Hash className="mr-2 h-4 w-4" />
                {t("downloader.byModelId")}
              </TabsTrigger>
              <TabsTrigger value="versionId" className="theme-ring">
                <GitBranch className="mr-2 h-4 w-4" />
                {t("downloader.byVersionId")}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="username" className="space-y-4 mt-4">
              <div className="space-y-2">
                <Label htmlFor="username" className="theme-text">
                  {t("downloader.username")}
                </Label>
                <Input
                  id="username"
                  placeholder={t("downloader.enterUsername")}
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  className="frosted-glass theme-border theme-ring"
                  disabled={isLoading}
                />
              </div>
              <Button
                onClick={() => handleDownload("username")}
                disabled={isLoading || !username.trim()}
                className="theme-primary theme-primary-hover w-full"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    下载中... ({downloadLimit[0]} 张)
                  </>
                ) : success ? (
                  <>
                    <Check className="mr-2 h-4 w-4" />
                    {t("downloader.downloaded")}
                  </>
                ) : (
                  <>
                    <Download className="mr-2 h-4 w-4" />
                    下载 {downloadLimit[0]} 张图像
                  </>
                )}
              </Button>
            </TabsContent>

            <TabsContent value="modelId" className="space-y-4 mt-4">
              <div className="space-y-2">
                <Label htmlFor="modelId" className="theme-text">
                  {t("downloader.modelId")}
                </Label>
                <Input
                  id="modelId"
                  placeholder={t("downloader.enterModelId")}
                  value={modelId}
                  onChange={(e) => setModelId(e.target.value)}
                  className="frosted-glass theme-border theme-ring"
                  disabled={isLoading}
                />
              </div>
              <Button
                onClick={() => handleDownload("modelId")}
                disabled={isLoading || !modelId.trim()}
                className="theme-primary theme-primary-hover w-full"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    下载中... ({downloadLimit[0]} 张)
                  </>
                ) : success ? (
                  <>
                    <Check className="mr-2 h-4 w-4" />
                    {t("downloader.downloaded")}
                  </>
                ) : (
                  <>
                    <Download className="mr-2 h-4 w-4" />
                    下载 {downloadLimit[0]} 张图像
                  </>
                )}
              </Button>
            </TabsContent>

            <TabsContent value="versionId" className="space-y-4 mt-4">
              <div className="space-y-2">
                <Label htmlFor="versionId" className="theme-text">
                  {t("downloader.versionId")}
                </Label>
                <Input
                  id="versionId"
                  placeholder={t("downloader.enterVersionId")}
                  value={modelVersionId}
                  onChange={(e) => setModelVersionId(e.target.value)}
                  className="frosted-glass theme-border theme-ring"
                  disabled={isLoading}
                />
              </div>
              <Button
                onClick={() => handleDownload("versionId")}
                disabled={isLoading || !modelVersionId.trim()}
                className="theme-primary theme-primary-hover w-full"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    下载中... ({downloadLimit[0]} 张)
                  </>
                ) : success ? (
                  <>
                    <Check className="mr-2 h-4 w-4" />
                    {t("downloader.downloaded")}
                  </>
                ) : (
                  <>
                    <Download className="mr-2 h-4 w-4" />
                    下载 {downloadLimit[0]} 张图像
                  </>
                )}
              </Button>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {error && (
        <Alert variant="destructive" className="frosted-glass">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Card className="frosted-card theme-border">
        <CardHeader>
          <CardTitle className="text-lg font-medium theme-text">使用说明</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 text-sm text-slate-700 dark:text-slate-300">
            <div>
              <h4 className="font-medium mb-2">API 限制说明：</h4>
              <ul className="list-disc list-inside space-y-1 text-xs">
                <li>
                  <strong>无 API 密钥：</strong>每次最多下载 100 张图像，每页最多 20 张
                </li>
                <li>
                  <strong>有 API 密钥：</strong>每次最多下载 10,000 张图像，每页最多 200 张
                </li>
                <li>API 有速率限制，下载大量图像时会自动添加延迟</li>
                <li>重复的图像会被自动过滤，不会重复添加</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">获取 API 密钥的好处：</h4>
              <ul className="list-disc list-inside space-y-1 text-xs">
                <li>可以下载更多图像（最多 10,000 张 vs 100 张）</li>
                <li>更快的下载速度（更少的延迟）</li>
                <li>访问更多的图像信息和元数据</li>
                <li>更稳定的 API 访问</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">调试信息：</h4>
              <ul className="list-disc list-inside space-y-1 text-xs">
                <li>当前API状态: {limits.hasApiKey ? "已认证" : "未认证"}</li>
                <li>每页限制: {limits.maxPerPage} 张</li>
                <li>总数限制: {limits.maxTotal} 张</li>
                <li>请检查浏览器控制台查看详细日志</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
