@echo off

:: =============================================================================
:: Start Electron Only (requires Next.js server running)
:: =============================================================================

title Electron Desktop App

echo.
echo Starting Electron Desktop App
echo =============================
echo.

echo This will start Electron and connect to the Next.js server.
echo IMPORTANT: Make sure Next.js dev server is running first!
echo           (Run start-nextjs.bat in another window)
echo.

:: Check if Electron is installed
npm list electron >nul 2>&1
if errorlevel 1 (
    echo [INFO] Installing Electron...
    npm install electron --save-dev
    if errorlevel 1 (
        echo [ERROR] Failed to install Electron
        pause
        exit /b 1
    )
)

:: Check if Next.js server is running
echo [INFO] Checking if Next.js server is running...
curl -s http://localhost:3000 >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Next.js server not detected on http://localhost:3000
    echo Please make sure to start Next.js first using start-nextjs.bat
    echo.
    set /p continue="Continue anyway? (y/n): "
    if /i not "%continue%"=="y" (
        echo Cancelled
        pause
        exit /b 0
    )
)

echo [INFO] Starting Electron...
echo [INFO] This will open the desktop application window
echo.

npm run electron

echo.
echo Electron application closed
pause
